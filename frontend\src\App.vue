<template>
  <div id="app">
    <el-container class="layout-container">
      <!-- 顶部导航栏 -->
      <el-header class="header">
        <div class="header-content">
          <div class="logo">
            <el-icon><Setting /></el-icon>
            <span class="title">SNMP-Modbus Bridge 配置管理</span>
          </div>
          <div class="header-info">
            <div v-if="authStore.isAuthenticated" class="user-info">
              <el-text type="info">欢迎，{{ authStore.username }}</el-text>
              <el-button @click="handleLogout" type="primary" link size="small">退出登录</el-button>
            </div>
            <el-text type="info">{{ currentTime }}</el-text>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边导航 -->
        <el-aside width="250px" class="sidebar">
          <el-menu
            :default-active="$route.path"
            router
            class="sidebar-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="/snmp-config">
              <el-icon><Setting /></el-icon>
              <span>SNMP-Modbus配置</span>
            </el-menu-item>
            <el-menu-item index="/config">
              <el-icon><DocumentEdit /></el-icon>
              <span>配置文件管理</span>
            </el-menu-item>
            <el-menu-item index="/network">
              <el-icon><Connection /></el-icon>
              <span>网络接口配置</span>
            </el-menu-item>
            <el-menu-item index="/whitelist">
              <el-icon><Shield /></el-icon>
              <span>NMS白名单管理</span>
            </el-menu-item>
            <el-menu-item index="/logs">
              <el-icon><Document /></el-icon>
              <span>日志查看器</span>
            </el-menu-item>
            <el-menu-item index="/backup">
              <el-icon><FolderOpened /></el-icon>
              <span>备份管理</span>
            </el-menu-item>
            <el-menu-item index="/status">
              <el-icon><Monitor /></el-icon>
              <span>系统状态</span>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区域 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const currentTime = ref('')

// 更新当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 菜单选择处理
const handleMenuSelect = (index) => {
  router.push(index)
}

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认登出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await authStore.logout()
    ElMessage.success('已成功登出')
    router.push('/login')
  } catch (error) {
    // 用户取消或登出失败
    if (error !== 'cancel') {
      console.error('登出失败:', error)
    }
  }
}

let timeInterval = null

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1000;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo .title {
  font-size: 20px;
  font-weight: 600;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 12px;
  background: rgba(255,255,255,0.1);
  border-radius: 6px;
  border: 1px solid rgba(255,255,255,0.2);
}

.sidebar {
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.sidebar-menu {
  border: none;
  background: transparent;
}

.sidebar-menu .el-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sidebar-menu .el-menu-item:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.main-content {
  background: #ffffff;
  padding: 20px;
  height: calc(100vh - 60px); /* 减去header高度 */
  overflow-y: auto;
  overflow-x: hidden;
}

@media (max-width: 768px) {
  .header-content {
    padding: 0 10px;
  }
  
  .logo .title {
    font-size: 16px;
  }
  
  .sidebar {
    width: 200px !important;
  }
  
  .main-content {
    padding: 15px;
  }
}
</style>