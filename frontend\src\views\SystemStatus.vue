<template>
  <div class="system-status">
    <div class="page-title">
      <el-icon><Monitor /></el-icon>
      系统状态
    </div>

    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" class="btn-primary" @click="refreshStatus">
          <el-icon><Refresh /></el-icon>
          刷新状态
        </el-button>
        <el-button type="info" @click="toggleAutoRefresh">
          <el-icon><Timer /></el-icon>
          {{ autoRefresh ? '停止自动刷新' : '开始自动刷新' }}
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-text type="info">
          最后更新: {{ lastUpdate }}
        </el-text>
      </div>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-grid" v-loading="loading">
      <!-- 服务状态 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <h3>服务状态</h3>
          </div>
        </template>
        <div class="status-content">
          <div class="status-item">
            <div class="status-label">后端API服务</div>
            <div class="status-value">
              <div class="status-indicator" :class="apiStatus.toLowerCase()">
                <el-icon v-if="apiStatus === 'healthy'"><CircleCheckFilled /></el-icon>
                <el-icon v-else><CircleCloseFilled /></el-icon>
                {{ apiStatus === 'healthy' ? '正常' : '异常' }}
              </div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-label">配置文件</div>
            <div class="status-value">
              <div class="status-indicator" :class="configFileExists ? 'up' : 'down'">
                <el-icon v-if="configFileExists"><DocumentChecked /></el-icon>
                <el-icon v-else><DocumentRemove /></el-icon>
                {{ configFileExists ? '存在' : '不存在' }}
              </div>
            </div>
          </div>
          <div class="status-item">
            <div class="status-label">网络连接</div>
            <div class="status-value">
              <div class="status-indicator" :class="networkStatus.toLowerCase()">
                <el-icon v-if="networkStatus === 'connected'"><Connection /></el-icon>
                <el-icon v-else><Close /></el-icon>
                {{ networkStatus === 'connected' ? '已连接' : '断开连接' }}
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 系统信息 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <h3>系统信息</h3>
          </div>
        </template>
        <div class="status-content">
          <el-descriptions :column="1" size="small">
            <el-descriptions-item label="操作系统">
              <el-text>{{ systemInfo.os || 'Linux' }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="Python版本">
              <el-text>{{ systemInfo.python_version || '3.8+' }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="服务版本">
              <el-text>{{ systemInfo.service_version || '1.0.0' }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="启动时间">
              <el-text>{{ systemInfo.start_time || '未知' }}</el-text>
            </el-descriptions-item>
            <el-descriptions-item label="运行时间">
              <el-text>{{ formatUptime(systemInfo.uptime) }}</el-text>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>

      <!-- 网络接口统计 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><Connection /></el-icon>
            <h3>网络接口统计</h3>
          </div>
        </template>
        <div class="status-content">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ networkStats.total }}</div>
              <div class="stat-label">总接口数</div>
            </div>
            <div class="stat-item success">
              <div class="stat-value">{{ networkStats.active }}</div>
              <div class="stat-label">活跃接口</div>
            </div>
            <div class="stat-item danger">
              <div class="stat-value">{{ networkStats.inactive }}</div>
              <div class="stat-label">非活跃接口</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 配置统计 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><DocumentEdit /></el-icon>
            <h3>配置统计</h3>
          </div>
        </template>
        <div class="status-content">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ configStats.sections }}</div>
              <div class="stat-label">配置节数</div>
            </div>
            <div class="stat-item info">
              <div class="stat-value">{{ configStats.items }}</div>
              <div class="stat-label">配置项数</div>
            </div>
            <div class="stat-item warning">
              <div class="stat-value">{{ configStats.backups }}</div>
              <div class="stat-label">备份文件数</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 最近活动 -->
      <el-card class="status-card full-width">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <h3>最近活动</h3>
          </div>
        </template>
        <div class="status-content">
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.timestamp"
              :type="activity.type"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
          <div v-if="recentActivities.length === 0" class="empty-activities">
            <el-text type="info">暂无最近活动记录</el-text>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSystemStore, useConfigStore, useNetworkStore } from '@/stores'

const systemStore = useSystemStore()
const configStore = useConfigStore()
const networkStore = useNetworkStore()

// 响应式数据
const loading = ref(false)
const autoRefresh = ref(false)
const lastUpdate = ref('')
const refreshInterval = ref(null)

const systemInfo = ref({
  os: 'Linux',
  python_version: '3.8+',
  service_version: '1.0.0',
  start_time: new Date().toISOString(),
  uptime: 0
})

const recentActivities = ref([
  {
    id: 1,
    title: '系统启动',
    description: 'Web配置管理工具启动成功',
    timestamp: new Date().toISOString(),
    type: 'primary'
  }
])

// 计算属性
const apiStatus = computed(() => {
  return systemStore.health.status === 'healthy' ? 'healthy' : 'error'
})

const configFileExists = computed(() => {
  return systemStore.health.config_file_exists
})

const networkStatus = computed(() => {
  return apiStatus.value === 'healthy' ? 'connected' : 'disconnected'
})

const networkStats = computed(() => {
  const interfaces = networkStore.interfaces
  return {
    total: interfaces.length,
    active: interfaces.filter(i => i.status === 'UP').length,
    inactive: interfaces.filter(i => i.status === 'DOWN').length
  }
})

const configStats = computed(() => {
  const sections = configStore.getSectionsList
  const totalItems = sections.reduce((sum, section) => sum + section.itemCount, 0)
  return {
    sections: sections.length,
    items: totalItems,
    backups: configStore.backups.length
  }
})

// 方法
const refreshStatus = async () => {
  try {
    loading.value = true
    
    // 并行加载所有状态数据
    await Promise.all([
      systemStore.checkHealth(),
      configStore.loadConfig(),
      configStore.loadBackups(),
      networkStore.loadInterfaces()
    ])
    
    lastUpdate.value = new Date().toLocaleString('zh-CN')
    
    // 添加活动记录
    addActivity('状态刷新', '系统状态已更新', 'success')
    
  } catch (error) {
    ElMessage.error('状态刷新失败')
    addActivity('状态刷新失败', error.message, 'danger')
  } finally {
    loading.value = false
  }
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshInterval.value = setInterval(refreshStatus, 30000) // 30秒刷新一次
    ElMessage.success('已开启自动刷新 (30秒间隔)')
    addActivity('自动刷新', '已开启自动状态刷新', 'primary')
  } else {
    if (refreshInterval.value) {
      clearInterval(refreshInterval.value)
      refreshInterval.value = null
    }
    ElMessage.info('已停止自动刷新')
    addActivity('自动刷新', '已停止自动状态刷新', 'info')
  }
}

const addActivity = (title, description, type = 'primary') => {
  const activity = {
    id: Date.now(),
    title,
    description,
    timestamp: new Date().toLocaleString('zh-CN'),
    type
  }
  
  recentActivities.value.unshift(activity)
  
  // 保持最近20条活动记录
  if (recentActivities.value.length > 20) {
    recentActivities.value = recentActivities.value.slice(0, 20)
  }
}

const formatUptime = (uptime) => {
  if (!uptime) return '未知'
  
  const hours = Math.floor(uptime / 3600)
  const minutes = Math.floor((uptime % 3600) / 60)
  const seconds = uptime % 60
  
  return `${hours}小时 ${minutes}分钟 ${seconds}秒`
}

// 生命周期
onMounted(() => {
  refreshStatus()
  
  // 模拟系统运行时间
  const startTime = Date.now()
  setInterval(() => {
    systemInfo.value.uptime = Math.floor((Date.now() - startTime) / 1000)
  }, 1000)
})

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})
</script>

<style scoped>
.system-status {
  padding: 0;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
}

.status-card {
  transition: all 0.3s ease;
}

.status-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.status-card.full-width {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 16px;
}

.status-content {
  padding: 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

.status-value {
  display: flex;
  align-items: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-item.success {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  color: #155724;
}

.stat-item.danger {
  background: linear-gradient(135deg, #fdf2f2 0%, #f8d7da 100%);
  color: #721c24;
}

.stat-item.info {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #0d47a1;
}

.stat-item.warning {
  background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
  color: #e65100;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 13px;
  opacity: 0.8;
}

.activity-content {
  margin-left: 8px;
}

.activity-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 13px;
  color: #606266;
}

.empty-activities {
  text-align: center;
  padding: 40px;
}

@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .stat-item {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 24px;
  }
}
</style>