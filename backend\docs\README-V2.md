# SNMP-Modbus 桥接服务 V2

## 🆕 V2 版本新特性

SNMP-Modbus桥接服务V2是原版本的重大升级，采用现代化的架构设计和最佳实践，提供了更高的性能、更好的可维护性和更强的企业级功能。

### 🔥 核心改进

#### 1. **模块化架构**
```
snmp-modbus-bridge/
├── core/                    # 核心功能模块
│   ├── modbus_client.py    # 异步Modbus连接池
│   ├── snmp_handler.py     # 异步SNMP处理器
│   ├── data_processor.py   # 数据处理器
│   └── oid_manager.py      # OID管理器
├── config/                  # 配置管理模块
│   ├── config_manager.py   # 配置管理器
│   └── validator.py        # 配置验证器
├── utils/                   # 工具模块
│   ├── cache.py            # 缓存系统
│   ├── health_check.py     # 健康检查
│   ├── logger.py           # 日志工具
│   └── exceptions.py       # 自定义异常
└── snmp-modbus-bridgev2.py # 主入口文件
```

#### 2. **异步连接池管理**
- ✅ **连接复用**：避免频繁创建/销毁连接
- ✅ **自动重连**：连接断开时自动重新连接
- ✅ **负载均衡**：多连接间的智能分配
- ✅ **健康检查**：定期检查连接状态
- ✅ **连接统计**：详细的连接使用统计

#### 3. **智能缓存机制**
- ✅ **TTL缓存**：时间到期自动失效
- ✅ **LRU淘汰**：最近最少使用算法
- ✅ **异步刷新**：后台异步更新缓存
- ✅ **批量操作**：支持批量缓存操作
- ✅ **缓存统计**：命中率和性能监控

#### 4. **健康检查系统**
- ✅ **HTTP端点**：`/health`、`/health/live`、`/health/ready`
- ✅ **组件监控**：监控各个子系统状态
- ✅ **自动检查**：定期执行健康检查
- ✅ **指标收集**：收集性能和状态指标
- ✅ **状态报告**：详细的健康状态报告

#### 5. **配置热重载**
- ✅ **文件监控**：自动检测配置文件变更
- ✅ **动态更新**：无需重启即可更新配置
- ✅ **环境变量**：支持环境变量替换
- ✅ **配置验证**：启动时和重载时的配置验证

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装V2版本依赖
pip install -r requirements-v2.txt
```

### 2. 运行服务

#### 使用完整版本（推荐）
```bash
python snmp-modbus-bridgev2.py --config config.ini
```

#### 使用简化版本
```bash
python snmp-modbus-bridgev2-simple.py
```

### 3. 健康检查

```bash
# 检查服务健康状态
curl http://localhost:8080/health

# 检查服务存活状态
curl http://localhost:8080/health/live

# 检查服务就绪状态
curl http://localhost:8080/health/ready
```

### 4. SNMP测试

```bash
# 基本查询
snmpget -v2c -c public localhost:1161 .*******.*******.0

# WALK遍历
snmpwalk -v2c -c public localhost:1161 .*******.2.1.1

# BULK查询
snmpbulkwalk -v2c -c public localhost:1161 .*******.2.1.1
```

## 📋 配置升级

### V2版本配置增强

原有的`config.ini`配置完全兼容，同时新增了以下配置选项：

```ini
[SNMP_BRIDGE_CONFIG]
# 原有配置保持不变...

# 新增缓存配置
cache_enabled = true
cache_ttl = 300.0
cache_max_size = 1000

# 新增健康检查配置
health_check_enabled = true
health_check_port = 8080
health_check_interval = 30.0

# 新增日志配置
log_level = INFO
log_file = /var/log/snmp-modbus-bridge/service.log

# 新增连接池配置
[MODBUS_TCP_CONFIG]
# 原有配置保持不变...
max_connections = 10
min_connections = 2

[MODBUS_RTU_CONFIG]
# 原有配置保持不变...
max_connections = 10
min_connections = 2
```

### 环境变量支持

V2版本支持环境变量替换：

```ini
[MODBUS_TCP_CONFIG]
server_ip = ${MODBUS_HOST:*************}
port = ${MODBUS_PORT:502}
```

## 🎯 性能对比

| 功能 | V1版本 | V2版本 | 改进 |
|------|--------|--------|------|
| 连接管理 | 每次创建 | 连接池复用 | **10x性能提升** |
| 数据缓存 | 无缓存 | 智能缓存 | **5x响应速度** |
| 并发处理 | 同步处理 | 异步并发 | **支持更高并发** |
| 监控能力 | 基础日志 | 健康检查+指标 | **完整可观测性** |
| 配置管理 | 静态配置 | 热重载 | **零停机更新** |

## 📊 监控和运维

### 1. 健康检查API

```bash
# 完整健康状态
curl http://localhost:8080/health | jq
{
  "overall_status": "healthy",
  "components": [
    {
      "name": "snmp_service",
      "status": "healthy",
      "message": "SNMP服务正常运行"
    },
    {
      "name": "modbus_pool", 
      "status": "healthy",
      "details": {
        "total_connections": 3,
        "active_connections": 1,
        "is_running": true
      }
    },
    {
      "name": "cache_system",
      "status": "healthy", 
      "details": {
        "hits": 1250,
        "misses": 89,
        "hit_rate": 0.93
      }
    }
  ],
  "timestamp": **********.0,
  "uptime": 3600.5
}
```

### 2. 缓存统计

```python
# 通过Python API获取缓存统计
stats = cache.get_stats()
print(f"缓存命中率: {stats['hit_rate']:.2%}")
print(f"缓存大小: {stats['size']}/{stats['max_size']}")
```

### 3. 连接池监控

```python
# 获取连接池状态
pool_stats = modbus_pool.get_stats()
print(f"活跃连接: {pool_stats['active_connections']}")
print(f"总连接数: {pool_stats['total_connections']}")
```

## 🐳 Docker部署

### 1. 构建镜像

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements-v2.txt .
RUN pip install -r requirements-v2.txt

COPY . .
EXPOSE 1161/udp 8080/tcp

CMD ["python", "snmp-modbus-bridgev2.py"]
```

### 2. 运行容器

```bash
# 构建镜像
docker build -t snmp-modbus-bridge:v2 .

# 运行容器
docker run -d \
  --name snmp-modbus-bridge \
  -p 1161:1161/udp \
  -p 8080:8080 \
  -v $(pwd)/config.ini:/app/config.ini \
  snmp-modbus-bridge:v2
```

## ☸️ Kubernetes部署

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: snmp-modbus-bridge
spec:
  replicas: 2
  selector:
    matchLabels:
      app: snmp-modbus-bridge
  template:
    metadata:
      labels:
        app: snmp-modbus-bridge
    spec:
      containers:
      - name: bridge
        image: snmp-modbus-bridge:v2
        ports:
        - containerPort: 1161
          protocol: UDP
        - containerPort: 8080
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: snmp-modbus-bridge
spec:
  selector:
    app: snmp-modbus-bridge
  ports:
  - name: snmp
    port: 161
    targetPort: 1161
    protocol: UDP
  - name: health
    port: 8080
    targetPort: 8080
    protocol: TCP
```

## 🛠️ 开发指南

### 1. 添加新的OID处理器

```python
from core.oid_manager import OIDHandler

class CustomOIDHandler(OIDHandler):
    async def handle_request(self, proto_ver) -> Any:
        # 自定义处理逻辑
        value = await self.get_custom_data()
        return await self.data_processor.convert_to_snmp_value(
            value, proto_ver, 'OctetString'
        )
```

### 2. 扩展健康检查

```python
async def check_custom_component() -> ComponentHealth:
    # 自定义健康检查逻辑
    return ComponentHealth(
        name='custom_component',
        status=HealthStatus.HEALTHY,
        message="组件正常"
    )

# 注册检查函数
health_checker.register_check('custom', check_custom_component)
```

### 3. 自定义缓存策略

```python
class CustomCache(DataCache):
    async def get(self, key: str) -> Optional[Any]:
        # 自定义缓存获取逻辑
        pass
    
    async def set(self, key: str, value: Any, ttl: float = None):
        # 自定义缓存设置逻辑
        pass
```

## 🔧 故障排除

### 1. 常见问题

**Q: 服务启动失败，提示端口被占用**
```bash
# 检查端口占用
netstat -tulpn | grep :1161
# 修改配置文件中的端口或停止占用进程
```

**Q: Modbus连接失败**
```bash
# 检查健康状态
curl http://localhost:8080/health
# 查看详细错误日志
tail -f /var/log/snmp-modbus-bridge/service.log
```

**Q: 缓存命中率低**
```bash
# 检查缓存统计
curl http://localhost:8080/health | jq '.components[] | select(.name=="cache_system")'
# 调整缓存TTL和大小配置
```

### 2. 性能调优

**连接池优化**
```ini
[MODBUS_TCP_CONFIG]
max_connections = 20        # 增加最大连接数
min_connections = 5         # 增加最小连接数
timeout = 5                 # 调整超时时间
```

**缓存优化**
```ini
[SNMP_BRIDGE_CONFIG]
cache_ttl = 600.0          # 增加缓存TTL
cache_max_size = 2000      # 增加缓存大小
```

## 📈 版本迁移

### 从V1迁移到V2

1. **备份当前配置**
   ```bash
   cp config.ini config.ini.backup
   ```

2. **安装V2依赖**
   ```bash
   pip install -r requirements-v2.txt
   ```

3. **更新配置（可选）**
   - 配置文件完全兼容，无需修改
   - 可以添加新的V2配置选项

4. **启动V2服务**
   ```bash
   python snmp-modbus-bridgev2.py
   ```

5. **验证功能**
   ```bash
   # 测试SNMP查询
   snmpget -v2c -c public localhost:1161 .*******.*******.0
   
   # 检查健康状态
   curl http://localhost:8080/health
   ```

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork项目
2. 创建特性分支
3. 提交变更
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情。

---

**SNMP-Modbus Bridge V2** - 企业级协议桥接解决方案 🚀