#!/bin/bash

# SNMP-Modbus Bridge Web配置管理工具启动脚本（前后端）
# 该脚本会同时启动前端和后端服务

echo "🚀 启动SNMP-Modbus Bridge Web配置管理工具（前后端）"
echo "=================================================="

# 检查Node.js环境
echo "📋 检查Node.js环境..."
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    echo "请安装Node.js: https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

# 检查Python环境
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

python3 --version

# 创建日志目录
mkdir -p logs

# 安装前端依赖
echo "📦 检查前端依赖..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
    echo "✅ 前端依赖安装完成"
else
    echo "✅ 前端依赖已存在"
fi
cd ..

# 检查后端依赖
echo "📦 检查后端依赖..."
cd backend
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
    echo "✅ 后端依赖检查完成"
else
    echo "⚠️  requirements.txt 文件不存在，跳过依赖安装"
fi
cd ..

# 启动后端服务
echo "🔧 启动后端API服务（包含SNMP桥接服务）..."
cd backend
python3 main.py > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../logs/backend.pid
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 5

# 检查后端是否启动成功
if kill -0 $BACKEND_PID 2>/dev/null; then
    echo "✅ 后端服务启动成功 (PID: $BACKEND_PID)"
else
    echo "❌ 后端服务启动失败"
    echo "请检查日志: logs/backend.log"
    exit 1
fi

# 启动前端服务
echo "🔧 启动前端开发服务器..."
cd frontend
npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../logs/frontend.pid
cd ..

# 等待前端启动
echo "⏳ 等待前端服务启动..."
sleep 5

# 检查前端是否启动成功
if kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "✅ 前端服务启动成功 (PID: $FRONTEND_PID)"
else
    echo "❌ 前端服务启动失败"
    echo "请检查日志: logs/frontend.log"
    # 停止后端服务
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

echo "✅ 服务启动完成！"
echo ""
echo "=========================================="
echo "  访问信息"
echo "=========================================="
echo ""
echo "前端应用: http://localhost:3000"
echo "后端API: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo ""
echo "默认登录信息:"
echo "用户名: admin"
echo "密码: admin1234"
echo ""
echo "=========================================="
echo ""
echo "提示: 使用 './stop.sh' 停止所有服务"
echo "按 Ctrl+C 也可以停止服务"
echo ""

# 创建停止脚本
cat > stop.sh << 'EOF'
#!/bin/bash

echo "停止Web配置管理工具服务..."

# 停止后端服务
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务已停止"
    fi
    rm -f logs/backend.pid
fi

# 停止前端服务
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止"
    fi
    rm -f logs/frontend.pid
fi

echo "✅ 所有服务已停止"
EOF

chmod +x stop.sh

# 等待中断信号
trap 'echo "\n正在停止服务..."; ./stop.sh; exit 0' SIGINT SIGTERM

# 保持运行
echo "进程正在后台运行..."
while true; do
    sleep 1
done