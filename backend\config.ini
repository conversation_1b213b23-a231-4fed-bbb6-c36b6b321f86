# =============================================================================
# SNMP-Modbus 桥接服务配置文件 (Linux 版本)
# =============================================================================

[SNMP_BRIDGE_CONFIG]
listen_ip = 0.0.0.0
listen_port = 1161
community = public
modbus_type = TCP
timezone_offset = +08
startup_delay = 2
error_value = -99998
# UDP并发配置
max_concurrent_requests = 100
max_oids_per_request = 30
# 日志配置
log_level = INFO
log_file = /var/log/snmp-modbus-bridge/service.log
log_max_size = 10MB
log_backup_count = 5

[MODBUS_TCP_CONFIG]
server_ip = 127.0.0.1
port = 502
timeout = 3
retry_interval = 10
update_interval = 5

[MODBUS_RTU_CONFIG]
port = /dev/ttyUSB0
baudrate = 9600
bytesize = 8
parity = N
stopbits = 1
timeout = 3
retry_interval = 10
update_interval = 5

[SYSTEM_OID_1]
oid = .*******.*******.0
description = System Description
type = fixed_value
value = SNMP-Modbus Bridge v1.0 (Linux)
snmp_data_type = OctetString

[SYSTEM_OID_2]
oid = .*******.4.1.41475.********.1.0
description = udSystemName
type = fixed_value
value = T-Block-UD
snmp_data_type = OctetString

; [SYSTEM_OID_3]
; oid = .*******.4.1.41475.********.2.0
; description = udSystemDesc
; type = fixed_value
; value = Industrial SNMP-Modbus Gateway
; snmp_data_type = OctetString

; [SYSTEM_OID_4]
; oid = .*******.4.1.41475.********.3.0
; description = udSystemManufacturer
; type = fixed_value
; value = Industrial Automation Co.
; snmp_data_type = OctetString

[SYSTEM_OID_5]
oid = .*******.4.1.41475.********.4.0
description = udSystemTime
type = utc_time
snmp_data_type = OctetString

[SYSTEM_OID_6]
oid = .*******.4.1.41475.********.*******
description = udEquipIndex
type = fixed_value
value = 1
snmp_data_type = OctetString

[SNMP_OID_1]
oid = .*******.4.1.41475.********.*******
description = temperature_sensor_1
register_address = 0x100
unit_id = 1
function_code = 3
data_type = int16
processing_type = multiply
coefficient = 0.1
offset = 0
decimal_places = 1
snmp_data_type = OctetString

[SNMP_OID_2]
oid = .*******.4.1.41475.********.*******
description = humidity_sensor_1
register_address = 0x101
unit_id = 1
function_code = 3
data_type = uint16
processing_type = multiply
coefficient = 0.01
offset = 0
decimal_places = 2
snmp_data_type = OctetString

[SNMP_OID_3]
oid = .*******.4.1.41475.*********.********.0
description = device_operation_mode
register_address = 0x102
unit_id = 1
function_code = 3
data_type = uint16
processing_type = direct
snmp_data_type = OctetString

[SNMP_OID_4]
oid = .*******.4.1.41475.*********.*******
description = communication_status
processing_type = communication_status
snmp_data_type = OctetString

# =============================================================================
# NMS白名单配置 - 支持IP+掩码格式
# 格式: ip/mask 或 ip/cidr
# 示例: ***********/24 或 *************/*************
# =============================================================================

[NMS_WHITELIST]
# 启用白名单功能
enable = true
# 默认策略: allow(允许所有) / deny(禁止所有)
default_policy = deny

# NMS白名单条目（最少10个）
[NMS_WHITELIST_1]
ip = ***********
mask = *************
description = Local Network
enable = true

[NMS_WHITELIST_2]
ip = 10.0.0.0
mask = *********
description = Private Network Class A
enable = true

[NMS_WHITELIST_3]
ip = **********
mask = ***********
description = Private Network Class B
enable = true

[NMS_WHITELIST_4]
ip = *********
mask = *********
description = Localhost
enable = true

[NMS_WHITELIST_5]
ip = ***********
mask = ***********
description = Private Network 192.168.x.x
enable = true

[NMS_WHITELIST_6]
ip = ***********
mask = *************
description = Test Network
enable = false

[NMS_WHITELIST_7]
ip = ************
mask = *************
description = Documentation Network
enable = false

[NMS_WHITELIST_8]
ip = 0.0.0.0
mask = 0.0.0.0
description = Allow All (use with caution)
enable = false

[NMS_WHITELIST_9]
ip = *************
mask = *************
description = Management Network
enable = false

[NMS_WHITELIST_10]
ip = *************
mask = *************
description = Monitoring Network
enable = false

# =============================================================================
# Web管理界面认证配置
# =============================================================================

[WEB_AUTH]
# 启用Web认证
enable = true
# 默认管理员账号
default_username = admin
# 默认密码（生产环境请修改）
default_password = admin123
# 会话超时时间（分钟）
session_timeout = 60
# 允许的管理IP地址（空表示允许所有）
allowed_ips = 127.0.0.1,***********/24
