# SNMP-Modbus Bridge Web配置管理工具 v2.0.0

## 功能概述

这是一个基于FastAPI和Vue.js的Web配置管理工具，专为SNMP-Modbus桥接服务设计。该工具提供了完整的桥接服务管理功能，包括自动启动和控制SNMP桥接服务。

## 新增功能 (v2.0.0)

### 🔧 SNMP桥接服务集成管理
- **自动启动**: FastAPI启动时自动启动SNMP桥接服务
- **服务控制**: 通过Web界面启动/停止/重启SNMP桥接服务
- **状态监控**: 实时查看SNMP桥接服务运行状态
- **日志查看**: 查看SNMP桥接服务运行日志

### 🔐 用户认证
- **默认登录**: admin / admin1234
- **会话管理**: JWT令牌认证机制
- **安全控制**: 所有API都需要认证访问

### 📋 配置管理
- **config.ini管理**: 完整的配置文件编辑功能
- **备份还原**: 自动备份配置文件，支持版本还原
- **热重载**: 配置变更后无需重启服务

### 🌐 网络配置
- **网络接口管理**: 配置Linux网络接口
- **IP地址设置**: 静态IP和DHCP配置
- **网关DNS设置**: 完整的网络参数配置

### 🛡️ NMS白名单管理
- **IP白名单**: 支持10个白名单条目配置
- **访问控制**: IP+子网掩码格式
- **动态管理**: 实时添加/修改/删除白名单

### 📊 日志管理
- **系统日志**: 查看系统运行日志
- **服务日志**: 专门的SNMP桥接服务日志
- **日志下载**: 支持日志文件下载

## API端点

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/user` - 获取用户信息

### SNMP桥接服务管理
- `GET /api/bridge/status` - 获取服务状态
- `POST /api/bridge/control` - 控制服务(启动/停止/重启)
- `GET /api/bridge/logs` - 获取服务日志

### 配置管理
- `GET /api/config` - 获取完整配置
- `GET /api/config/{section}` - 获取配置节
- `PUT /api/config/{section}/{key}` - 更新配置项
- `POST /api/config/section` - 创建配置节
- `DELETE /api/config/{section}` - 删除配置节

### 白名单管理
- `GET /api/whitelist` - 获取白名单配置
- `PUT /api/whitelist/{entry_id}` - 更新白名单条目
- `PUT /api/whitelist/config` - 更新白名单全局配置

### 网络管理
- `GET /api/network/interfaces` - 获取网络接口
- `GET /api/network/interfaces/{name}` - 获取指定接口
- `PUT /api/network/interfaces/{name}` - 配置网络接口

### 日志管理
- `GET /api/logs` - 获取日志文件列表
- `GET /api/logs/{file_name}` - 读取日志文件
- `GET /api/logs/download/{file_name}` - 下载日志文件

## 启动方式

### 🏭 生产环境部署（推荐）

**Step 1: 构建前端**

*Windows:*
```cmd
cd web-config-manager
build.bat
```

*Linux/Mac:*
```bash
cd web-config-manager
chmod +x build.sh
./build.sh
```

**Step 2: 启动生产服务**

*Windows:*
```cmd
start-prod.bat
```

*Linux/Mac:*
```bash
chmod +x start-prod.sh
./start-prod.sh
```

**访问地址**: http://localhost:8000

### 🚀 方式1: 一键启动（开发环境）

**Windows:**
```cmd
cd web-config-manager
start.bat
```

**Linux/Mac:**
```bash
cd web-config-manager
./start.sh
```

这将同时启动前端和后端服务：
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 🔧 方式2: 分别启动（开发调试）

**启动后端服务:**
```bash
cd web-config-manager/backend
python3 main.py
```

**启动前端服务:**
```bash
cd web-config-manager/frontend
npm install  # 首次运行
npm run dev
```

### 🛑 停止服务

**Windows:** 关闭对应的命令行窗口

**Linux/Mac:**
```bash
./stop.sh
```
或按 `Ctrl+C`

## 服务架构

```
FastAPI后端服务 (端口8000)
    ↓ 自动启动
SNMP-Modbus桥接服务 (snmp-modbus-bridgev2.py)
    ↓ 协议转换
Modbus设备 ←→ SNMP客户端
```

## 技术特性

- **模块化设计**: 清晰的代码组织结构
- **异步处理**: 基于FastAPI的高性能异步处理
- **进程管理**: 自动管理SNMP桥接服务生命周期
- **错误处理**: 完善的异常处理和错误提示
- **日志记录**: 详细的操作日志和状态记录

## 配置说明

### 默认认证配置
```ini
[WEB_AUTH]
enable = true
default_username = admin
default_password = admin1234
session_timeout = 60
```

### SNMP桥接服务配置
- 服务脚本路径: `../snmp-modbus-bridgev2.py`
- 自动启动: 是
- 进程管理: 支持启动/停止/重启
- 状态监控: 实时状态检查

## 安全注意事项

1. **修改默认密码**: 生产环境请修改默认登录密码
2. **网络访问**: 建议在受信任网络中运行
3. **权限控制**: 确保运行用户有适当的系统权限
4. **防火墙设置**: 适当配置防火墙规则

## 故障排除

### SNMP桥接服务无法启动
1. 检查`snmp-modbus-bridgev2.py`文件是否存在
2. 检查Python依赖是否安装完整
3. 查看服务日志获取详细错误信息

### 网络接口配置失败
1. 确认运行用户有网络配置权限
2. 检查网络接口名称是否正确
3. 验证IP地址和子网掩码格式

### 配置文件无法修改
1. 检查config.ini文件权限
2. 确认配置文件格式正确
3. 查看备份目录权限设置

## 版本历史

- **v2.0.0**: 集成SNMP桥接服务管理，默认密码修改为admin1234
- **v1.0.0**: 基础Web配置管理功能

---

更多详细信息请参考API文档: http://localhost:8000/docs
