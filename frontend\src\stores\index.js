import { defineStore } from 'pinia'
import { configApi, networkApi, systemApi, authApi, whitelistApi, logApi } from '@/utils/api'
import { setAuthToken, clearAuthToken, getAuthToken } from '@/utils/api'

export const useConfigStore = defineStore('config', {
  state: () => ({
    config: {},
    configSections: [],
    backups: [],
    loading: false,
  }),

  getters: {
    getConfigSection: (state) => (sectionName) => {
      return state.config[sectionName] || {}
    },
    
    getSectionsList: (state) => {
      return Object.keys(state.config).map(name => ({
        name,
        items: state.config[name],
        itemCount: Object.keys(state.config[name] || {}).length
      }))
    },
  },

  actions: {
    async loadConfig() {
      try {
        this.loading = true
        const response = await configApi.getConfig()
        this.config = response.data
        return response
      } catch (error) {
        console.error('加载配置失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateConfigItem(section, key, value) {
      try {
        const response = await configApi.updateConfigItem(section, key, value)
        
        // 更新本地状态
        if (!this.config[section]) {
          this.config[section] = {}
        }
        this.config[section][key] = value
        
        return response
      } catch (error) {
        console.error('更新配置项失败:', error)
        throw error
      }
    },

    async createConfigSection(sectionData) {
      try {
        const response = await configApi.createConfigSection(sectionData)
        
        // 更新本地状态
        this.config[sectionData.name] = sectionData.items
        
        return response
      } catch (error) {
        console.error('创建配置节失败:', error)
        throw error
      }
    },

    async deleteConfigSection(section) {
      try {
        const response = await configApi.deleteConfigSection(section)
        
        // 更新本地状态
        delete this.config[section]
        
        return response
      } catch (error) {
        console.error('删除配置节失败:', error)
        throw error
      }
    },

    async deleteConfigItem(section, key) {
      try {
        const response = await configApi.deleteConfigItem(section, key)
        
        // 更新本地状态
        if (this.config[section]) {
          delete this.config[section][key]
        }
        
        return response
      } catch (error) {
        console.error('删除配置项失败:', error)
        throw error
      }
    },

    async loadBackups() {
      try {
        const response = await configApi.getBackups()
        this.backups = response.data
        return response
      } catch (error) {
        console.error('加载备份列表失败:', error)
        throw error
      }
    },

    async createBackup() {
      try {
        const response = await configApi.createBackup()
        // 重新加载备份列表
        await this.loadBackups()
        return response
      } catch (error) {
        console.error('创建备份失败:', error)
        throw error
      }
    },

    async restoreConfig(backupFilename) {
      try {
        const response = await configApi.restoreConfig(backupFilename)
        // 重新加载配置
        await this.loadConfig()
        return response
      } catch (error) {
        console.error('恢复配置失败:', error)
        throw error
      }
    },

    async uploadConfig(file) {
      try {
        const response = await configApi.uploadConfig(file)
        // 重新加载配置
        await this.loadConfig()
        return response
      } catch (error) {
        console.error('上传配置失败:', error)
        throw error
      }
    },
  },
})

export const useNetworkStore = defineStore('network', {
  state: () => ({
    interfaces: [],
    loading: false,
  }),

  getters: {
    getInterface: (state) => (name) => {
      return state.interfaces.find(iface => iface.name === name)
    },
    
    activeInterfaces: (state) => {
      return state.interfaces.filter(iface => iface.status === 'UP')
    },
    
    downInterfaces: (state) => {
      return state.interfaces.filter(iface => iface.status === 'DOWN')
    },
  },

  actions: {
    async loadInterfaces() {
      try {
        this.loading = true
        const response = await networkApi.getInterfaces()
        this.interfaces = response.data
        return response
      } catch (error) {
        console.error('加载网络接口失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async configureInterface(interfaceName, config) {
      try {
        const response = await networkApi.configureInterface(interfaceName, config)
        
        // 更新本地状态
        const index = this.interfaces.findIndex(iface => iface.name === interfaceName)
        if (index !== -1) {
          this.interfaces[index] = { ...this.interfaces[index], ...config }
        }
        
        return response
      } catch (error) {
        console.error('配置网络接口失败:', error)
        throw error
      }
    },
  },
})

export const useSystemStore = defineStore('system', {
  state: () => ({
    health: {
      status: 'unknown',
      timestamp: null,
      config_file_exists: false,
    },
    loading: false,
  }),

  actions: {
    async checkHealth() {
      try {
        const response = await systemApi.healthCheck()
        this.health = response
        return response
      } catch (error) {
        console.error('健康检查失败:', error)
        this.health.status = 'error'
        throw error
      }
    },
  },
})

// 认证管理Store
export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: getAuthToken(),
    isLoggedIn: !!getAuthToken(),
    loading: false,
  }),

  getters: {
    isAuthenticated: (state) => state.isLoggedIn && !!state.token,
    username: (state) => state.user?.username || 'Anonymous',
  },

  actions: {
    async login(credentials) {
      try {
        this.loading = true
        const response = await authApi.login(credentials)
        
        if (response.success && response.token) {
          this.token = response.token
          this.isLoggedIn = true
          setAuthToken(response.token)
          
          // 获取用户信息
          await this.getUserInfo()
        }
        
        return response
      } catch (error) {
        console.error('登录失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async logout() {
      try {
        await authApi.logout()
      } catch (error) {
        console.error('登出请求失败:', error)
      } finally {
        this.token = null
        this.user = null
        this.isLoggedIn = false
        clearAuthToken()
      }
    },

    async getUserInfo() {
      try {
        const response = await authApi.getUserInfo()
        this.user = response.data
        return response
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果获取用户信息失败，清除登录状态
        await this.logout()
        throw error
      }
    },

    async checkAuthStatus() {
      if (this.token) {
        try {
          await this.getUserInfo()
        } catch (error) {
          await this.logout()
        }
      }
    },
  },
})

// NMS白名单管理Store
export const useWhitelistStore = defineStore('whitelist', {
  state: () => ({
    config: {
      enable: true,
      default_policy: 'deny',
      entries: []
    },
    loading: false,
  }),

  getters: {
    enabledEntries: (state) => {
      return state.config.entries.filter(entry => entry.enable)
    },
    
    totalEntries: (state) => state.config.entries.length,
  },

  actions: {
    async loadWhitelist() {
      try {
        this.loading = true
        const response = await whitelistApi.getWhitelist()
        this.config = response.data
        return response
      } catch (error) {
        console.error('加载白名单配置失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateEntry(entryId, entry) {
      try {
        const response = await whitelistApi.updateEntry(entryId, entry)
        
        // 更新本地状态
        const index = this.config.entries.findIndex(e => e.id === entryId)
        if (index !== -1) {
          this.config.entries[index] = { id: entryId, ...entry }
        } else {
          this.config.entries.push({ id: entryId, ...entry })
        }
        
        return response
      } catch (error) {
        console.error('更新白名单条目失败:', error)
        throw error
      }
    },

    async updateGlobalConfig(config) {
      try {
        const response = await whitelistApi.updateConfig(config)
        
        // 更新本地状态
        Object.assign(this.config, config)
        
        return response
      } catch (error) {
        console.error('更新白名单全局配置失败:', error)
        throw error
      }
    },
  },
})

// 日志管理Store
export const useLogStore = defineStore('log', {
  state: () => ({
    logFiles: [],
    currentLog: {
      lines: [],
      total: 0,
      file_path: ''
    },
    loading: false,
  }),

  getters: {
    availableLogFiles: (state) => {
      return state.logFiles.map(file => ({
        label: file.name,
        value: file.name,
        size: file.size,
        path: file.path,
        modified_time: file.modified_time
      }))
    },
  },

  actions: {
    async loadLogFiles() {
      try {
        this.loading = true
        const response = await logApi.getLogFiles()
        this.logFiles = response.data
        return response
      } catch (error) {
        console.error('加载日志文件列表失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async readLog(fileName, params = {}) {
      try {
        this.loading = true
        const response = await logApi.readLog(fileName, params)
        this.currentLog = response.data
        return response
      } catch (error) {
        console.error('读取日志文件失败:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    async downloadLog(fileName) {
      try {
        const response = await logApi.downloadLog(fileName)
        
        // 创建下载链接
        const blob = new Blob([response.data], { type: 'application/octet-stream' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        return response
      } catch (error) {
        console.error('下载日志文件失败:', error)
        throw error
      }
    },
  },
})