#!/bin/bash

# SNMP-Modbus Bridge Web配置管理工具 - 一键生产部署脚本

echo "🚀 一键构建并启动生产环境"
echo "=================================================="
echo ""

echo "⏱️ 这个过程可能需要几分钟时间，请耐心等待..."
echo ""

# 第一步：构建前端
echo "📦 Step 1/2: 构建前端静态文件..."
./build.sh
if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

echo ""
echo "✅ 前端构建完成"

# 第二步：启动生产服务
echo "🔧 Step 2/2: 启动生产环境服务..."
echo ""

./start-prod.sh

# Web配置管理工具启动脚本
# 适用于 Debian/Ubuntu 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="snmp-modbus-bridge-web-config"
PROJECT_DIR="/opt/snmp-modbus-bridge"
WEB_CONFIG_DIR="$PROJECT_DIR/web-config-manager"
BACKEND_DIR="$WEB_CONFIG_DIR/backend"
FRONTEND_DIR="$WEB_CONFIG_DIR/frontend"

# 服务配置
BACKEND_PORT=8000
FRONTEND_PORT=3000

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查操作系统
    if ! grep -q "Ubuntu\|Debian" /etc/os-release; then
        log_warning "此脚本专为 Ubuntu/Debian 系统设计"
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装，请先安装 Python 3.7+"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装，请先安装 pip3"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js 16+"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    sudo apt-get update
    sudo apt-get install -y \
        python3-pip \
        python3-venv \
        net-tools \
        iproute2 \
        nginx \
        supervisor
    
    log_success "系统依赖安装完成"
}

# 创建项目目录
create_directories() {
    log_info "创建项目目录..."
    
    sudo mkdir -p "$PROJECT_DIR"
    sudo mkdir -p "$WEB_CONFIG_DIR"
    sudo mkdir -p "$BACKEND_DIR"
    sudo mkdir -p "$FRONTEND_DIR"
    sudo mkdir -p "/var/log/snmp-modbus-bridge"
    
    # 设置目录权限
    sudo chown -R $USER:$USER "$PROJECT_DIR"
    sudo chown -R $USER:$USER "/var/log/snmp-modbus-bridge"
    
    log_success "项目目录创建完成"
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件..."
    
    # 复制后端文件
    cp -r backend/* "$BACKEND_DIR/"
    
    # 复制前端文件
    cp -r frontend/* "$FRONTEND_DIR/"
    
    # 复制主配置文件
    if [ -f "../config.ini" ]; then
        cp "../config.ini" "$PROJECT_DIR/"
    else
        log_warning "未找到主配置文件 config.ini"
    fi
    
    log_success "项目文件复制完成"
}

# 安装后端依赖
install_backend_dependencies() {
    log_info "安装后端依赖..."
    
    cd "$BACKEND_DIR"
    
    # 创建虚拟环境
    python3 -m venv venv
    source venv/bin/activate
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    pip install -r requirements.txt
    
    log_success "后端依赖安装完成"
}

# 安装前端依赖
install_frontend_dependencies() {
    log_info "安装前端依赖..."
    
    cd "$FRONTEND_DIR"
    
    # 安装依赖
    npm install
    
    # 构建前端
    npm run build
    
    log_success "前端依赖安装和构建完成"
}

# 配置Supervisor
configure_supervisor() {
    log_info "配置Supervisor..."
    
    # 创建Supervisor配置文件
    sudo tee /etc/supervisor/conf.d/snmp-modbus-bridge-web.conf > /dev/null <<EOF
[program:snmp-modbus-bridge-backend]
command=$BACKEND_DIR/venv/bin/python $BACKEND_DIR/main.py
directory=$BACKEND_DIR
user=$USER
autostart=true
autorestart=true
stdout_logfile=/var/log/snmp-modbus-bridge/backend.log
stderr_logfile=/var/log/snmp-modbus-bridge/backend_error.log
environment=PATH="$BACKEND_DIR/venv/bin"

[group:snmp-modbus-bridge-web]
programs=snmp-modbus-bridge-backend
priority=999
EOF
    
    # 重新加载Supervisor配置
    sudo supervisorctl reread
    sudo supervisorctl update
    
    log_success "Supervisor配置完成"
}

# 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 创建Nginx配置文件
    sudo tee /etc/nginx/sites-available/snmp-modbus-bridge-web > /dev/null <<EOF
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location / {
        root $FRONTEND_DIR/dist;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:$BACKEND_PORT;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        root $FRONTEND_DIR/dist;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    # 启用站点
    sudo ln -sf /etc/nginx/sites-available/snmp-modbus-bridge-web /etc/nginx/sites-enabled/
    
    # 测试Nginx配置
    sudo nginx -t
    
    # 重启Nginx
    sudo systemctl restart nginx
    
    log_success "Nginx配置完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动后端服务
    sudo supervisorctl start snmp-modbus-bridge-backend
    
    # 启动Nginx
    sudo systemctl enable nginx
    sudo systemctl start nginx
    
    log_success "服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查后端服务
    if sudo supervisorctl status snmp-modbus-bridge-backend | grep -q "RUNNING"; then
        log_success "后端服务运行正常"
    else
        log_error "后端服务启动失败"
        sudo supervisorctl status snmp-modbus-bridge-backend
    fi
    
    # 检查Nginx
    if sudo systemctl is-active --quiet nginx; then
        log_success "Nginx服务运行正常"
    else
        log_error "Nginx服务启动失败"
        sudo systemctl status nginx
    fi
    
    # 检查端口
    if netstat -ln | grep -q ":$BACKEND_PORT"; then
        log_success "后端API服务监听端口 $BACKEND_PORT"
    else
        log_warning "后端API服务未监听端口 $BACKEND_PORT"
    fi
    
    if netstat -ln | grep -q ":80"; then
        log_success "Web服务监听端口 80"
    else
        log_warning "Web服务未监听端口 80"
    fi
}

# 显示访问信息
show_access_info() {
    log_info "部署完成！"
    echo ""
    echo "=========================================="
    echo "  SNMP-Modbus Bridge Web配置管理工具"
    echo "=========================================="
    echo ""
    echo "Web界面访问地址:"
    echo "  http://localhost"
    echo "  http://$(hostname -I | awk '{print $1}')"
    echo ""
    echo "后端API地址:"
    echo "  http://localhost:$BACKEND_PORT"
    echo "  http://$(hostname -I | awk '{print $1}'):$BACKEND_PORT"
    echo ""
    echo "API文档地址:"
    echo "  http://localhost:$BACKEND_PORT/docs"
    echo ""
    echo "服务管理命令:"
    echo "  查看服务状态: sudo supervisorctl status"
    echo "  重启后端服务: sudo supervisorctl restart snmp-modbus-bridge-backend"
    echo "  重启Web服务: sudo systemctl restart nginx"
    echo ""
    echo "日志文件位置:"
    echo "  后端日志: /var/log/snmp-modbus-bridge/backend.log"
    echo "  错误日志: /var/log/snmp-modbus-bridge/backend_error.log"
    echo "  Nginx日志: /var/log/nginx/"
    echo ""
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  SNMP-Modbus Bridge Web配置管理工具"
    echo "  部署脚本 v1.0.0"
    echo "=========================================="
    echo ""
    
    check_requirements
    install_system_dependencies
    create_directories
    copy_project_files
    install_backend_dependencies
    install_frontend_dependencies
    configure_supervisor
    configure_nginx
    start_services
    check_services
    show_access_info
}

# 运行主函数
main "$@"