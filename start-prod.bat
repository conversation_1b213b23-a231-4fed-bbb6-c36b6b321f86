@echo off
chcp 65001 >nul
title SNMP-Modbus Bridge Web配置管理工具 - 生产环境

echo 🚀 启动SNMP-Modbus Bridge Web配置管理工具（生产环境）
echo ==================================================

rem 检查Python环境
echo 📋 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH环境变量
    pause
    exit /b 1
)

python --version

rem 检查前端构建文件
echo 📋 检查前端构建文件...
if not exist frontend\dist (
    echo ❌ 前端构建文件不存在，请先运行构建脚本:
    echo    build.bat
    pause
    exit /b 1
)

if not exist frontend\dist\index.html (
    echo ❌ 前端入口文件不存在
    pause
    exit /b 1
)

echo ✅ 前端构建文件检查通过

rem 检查后端依赖
echo 📦 检查后端依赖...
cd backend
if exist requirements.txt (
    pip install -r requirements.txt >nul 2>&1
    echo ✅ 后端依赖检查完成
) else (
    echo ⚠️  requirements.txt 文件不存在，跳过依赖安装
)

rem 启动生产环境服务
echo 🔧 启动生产环境服务...
echo 访问地址: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo 默认登录用户名: admin
echo 默认登录密码: admin1234
echo ==================================================
echo 按 Ctrl+C 停止服务...
echo.

python main.py

pause