#!/bin/bash

# SNMP-Modbus Bridge Web配置管理工具 - 生产环境构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_info "开始构建生产环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    log_error "Node.js未安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_error "npm未安装"
    exit 1
fi

# 进入前端目录
log_info "进入前端目录..."
cd frontend

# 安装依赖
log_info "安装前端依赖..."
npm install

# 清理旧的构建文件
if [ -d "dist" ]; then
    log_info "清理旧的构建文件..."
    rm -rf dist
fi

# 构建生产环境
log_info "开始构建前端生产环境..."
npm run build

# 检查构建结果
if [ ! -d "dist" ]; then
    log_error "前端构建失败，dist目录不存在"
    exit 1
fi

if [ ! -f "dist/index.html" ]; then
    log_error "前端构建失败，index.html文件不存在"
    exit 1
fi

log_success "前端构建完成！"

# 显示构建结果
echo ""
echo "=========================================="
echo "  构建结果"
echo "=========================================="
echo ""
echo "构建目录: $(pwd)/dist"
echo "入口文件: $(pwd)/dist/index.html"
echo ""

# 显示dist目录结构
if command -v tree &> /dev/null; then
    echo "目录结构:"
    tree dist -L 2
else
    echo "文件列表:"
    ls -la dist/
fi

echo ""
echo "=========================================="
echo "  部署说明"
echo "=========================================="
echo ""
echo "1. 生产环境运行方式:"
echo "   cd ../backend"
echo "   python main.py"
echo ""
echo "2. 访问地址:"
echo "   http://localhost:8000"
echo ""
echo "3. 默认登录信息:"
echo "   用户名: admin"
echo "   密码: admin1234"
echo ""
echo "=========================================="

cd ..
log_success "构建脚本执行完成！"