#!/usr/bin/env python3
"""
自定义异常类

定义SNMP-Modbus桥接服务专用的异常类，提供更精确的错误处理和诊断信息。

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

class SNMPModbusException(Exception):
    """SNMP-Modbus桥接服务基础异常类"""
    def __init__(self, message: str, error_code: int = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or -99999
        self.details = details or {}
    
    def __str__(self):
        if self.details:
            return f"{self.message} (错误码: {self.error_code}, 详情: {self.details})"
        return f"{self.message} (错误码: {self.error_code})"

class ModbusConnectionError(SNMPModbusException):
    """Modbus连接异常"""
    def __init__(self, message: str, host: str = None, port: int = None):
        super().__init__(message, error_code=-99998)
        if host and port:
            self.details.update({"host": host, "port": port})

class ModbusReadError(SNMPModbusException):
    """Modbus读取异常"""
    def __init__(self, message: str, register_address: int = None, unit_id: int = None):
        super().__init__(message, error_code=-99997)
        if register_address is not None:
            self.details["register_address"] = register_address
        if unit_id is not None:
            self.details["unit_id"] = unit_id

class ConfigurationError(SNMPModbusException):
    """配置错误异常"""
    def __init__(self, message: str, config_section: str = None):
        super().__init__(message, error_code=-99996)
        if config_section:
            self.details["config_section"] = config_section

class SNMPError(SNMPModbusException):
    """SNMP处理异常"""
    def __init__(self, message: str, oid: str = None):
        super().__init__(message, error_code=-99995)
        if oid:
            self.details["oid"] = oid

class CacheError(SNMPModbusException):
    """缓存操作异常"""
    def __init__(self, message: str, cache_key: str = None):
        super().__init__(message, error_code=-99994)
        if cache_key:
            self.details["cache_key"] = cache_key

class HealthCheckError(SNMPModbusException):
    """健康检查异常"""
    def __init__(self, message: str, component: str = None):
        super().__init__(message, error_code=-99993)
        if component:
            self.details["component"] = component