"""
配置管理器

支持配置文件加载、验证、热重载等功能

作者: SNMP-Modbus Bridge Team V2
版本: 2.2.0
"""

import os
import asyncio
import hashlib
import importlib
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SNMPConfig:
    """SNMP配置"""
    listen_ip: str = "0.0.0.0"
    listen_port: int = 1161
    community: str = "public"

@dataclass
class ModbusConfig:
    """Modbus配置"""
    connection_type: str = "TCP"
    server_ip: str = "127.0.0.1"
    port: int = 502
    device: str = "/dev/ttyUSB0"
    baudrate: int = 9600
    bytesize: int = 8
    parity: str = "N"
    stopbits: int = 1
    timeout: float = 3.0
    max_connections: int = 10
    min_connections: int = 1

@dataclass
class OIDMapping:
    """OID映射配置"""
    oid: str
    description: str
    oid_type: str  # 'system' 或 'modbus'
    snmp_data_type: str = "OctetString"
    
    # 系统OID专用字段
    system_type: Optional[str] = None  # 'fixed_value', 'uptime', 'utc_time'
    system_value: Optional[Any] = None
    
    # Modbus OID专用字段
    register_address: Optional[int] = None
    unit_id: Optional[int] = None
    function_code: Optional[int] = None
    data_type: str = "uint16"
    processing_type: str = "direct"  # 'direct', 'multiply', 'communication_status'
    coefficient: float = 1.0
    offset: float = 0.0
    decimal_places: int = 0

@dataclass
class SystemConfig:
    """系统配置"""
    log_level: str = "INFO"
    log_file: Optional[str] = None
    timezone_offset: str = "+08"
    cache_enabled: bool = True
    cache_max_size: int = 1000
    cache_ttl: float = 300.0
    health_check_enabled: bool = True
    health_check_port: int = 8080
    health_check_interval: float = 30.0

class ConfigMonitor:
    """配置文件监控器"""
    
    def __init__(self, config_file: str, check_interval: float = 3.0):
        self.config_file = Path(config_file)
        self.check_interval = check_interval
        self.last_hash = None
        self.last_modified = None
        self.is_monitoring = False
        self.reload_callback: Optional[Callable] = None
        
        # 计算初始哈希值
        self._update_file_info()
        
        logger.info(f"初始化配置文件监控器: {self.config_file}")
    
    def _update_file_info(self):
        """更新文件信息"""
        try:
            if self.config_file.exists():
                # 计算文件哈希值
                with open(self.config_file, 'rb') as f:
                    content = f.read()
                    self.last_hash = hashlib.md5(content).hexdigest()
                
                # 获取修改时间
                self.last_modified = self.config_file.stat().st_mtime
                logger.debug(f"配置文件信息更新: hash={self.last_hash[:8]}, mtime={self.last_modified}")
            else:
                logger.warning(f"配置文件不存在: {self.config_file}")
        except Exception as e:
            logger.error(f"获取配置文件信息失败: {e}")
    
    def has_changed(self) -> bool:
        """检查文件是否有变化"""
        try:
            if not self.config_file.exists():
                return False
            
            # 检查修改时间
            current_modified = self.config_file.stat().st_mtime
            if current_modified != self.last_modified:
                # 再检查哈希值
                with open(self.config_file, 'rb') as f:
                    content = f.read()
                    current_hash = hashlib.md5(content).hexdigest()
                
                if current_hash != self.last_hash:
                    logger.info(f"🔄 检测到配置文件变化: {self.config_file}")
                    old_hash = self.last_hash[:8] if self.last_hash else "None"
                    logger.debug(f"哈希值变化: {old_hash} -> {current_hash[:8]}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查配置文件变化失败: {e}")
            return False
    
    def set_reload_callback(self, callback: Callable):
        """设置重载回调函数"""
        self.reload_callback = callback
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        logger.info(f"✅ 开始监控配置文件变化 (检查间隔: {self.check_interval}秒)")
        
        while self.is_monitoring:
            try:
                if self.has_changed():
                    logger.info("🔄 检测到配置文件更改，触发热重载...")
                    
                    # 等待一小段时间，确保文件写入完成
                    await asyncio.sleep(0.5)
                    
                    # 更新文件信息
                    self._update_file_info()
                    
                    # 调用重载回调
                    if self.reload_callback:
                        try:
                            await self.reload_callback()
                        except Exception as e:
                            logger.error(f"配置重载回调失败: {e}")
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控配置文件异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.is_monitoring = False
            logger.info("✅ 停止监控配置文件变化")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        return {
            'config_file': str(self.config_file),
            'is_monitoring': self.is_monitoring,
            'check_interval': self.check_interval,
            'last_hash': self.last_hash[:8] if self.last_hash else None,
            'last_modified': self.last_modified,
            'file_exists': self.config_file.exists()
        }

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = 'config.ini'):
        self.config_file = config_file
        self.snmp_config = SNMPConfig()
        self.modbus_config = ModbusConfig()
        self.system_config = SystemConfig()
        self.oid_mappings: List[OIDMapping] = []
        
        # 配置监控器
        self.monitor = ConfigMonitor(config_file)
        self.monitor.set_reload_callback(self._on_config_reload)
        
        # 重载回调列表
        self.reload_callbacks: List[Callable] = []
        
        # 重载统计
        self.reload_count = 0
        self.last_reload_time = None
        
        logger.info(f"初始化配置管理器: {config_file}")
    
    async def load_config(self):
        """加载配置文件"""
        try:
            logger.info("📋 加载配置文件...")
            
            # 重新导入配置加载器模块
            from ..utils import config_loader
            importlib.reload(config_loader)
            
            # 加载各项配置
            self._load_snmp_config(config_loader)
            self._load_modbus_config(config_loader)
            self._load_system_config(config_loader)
            self._load_oid_mappings(config_loader)
            
            logger.info("✅ 配置文件加载完成")
            
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise
    
    def _load_snmp_config(self, config_loader):
        """加载SNMP配置"""
        bridge_config = getattr(config_loader, 'SNMP_BRIDGE_CONFIG', {})
        self.snmp_config.listen_ip = bridge_config.get('listen_ip', '0.0.0.0')
        self.snmp_config.listen_port = bridge_config.get('listen_port', 1161)
        self.snmp_config.community = bridge_config.get('community', 'public')
    
    def _load_modbus_config(self, config_loader):
        """加载Modbus配置"""
        modbus_type = getattr(config_loader, 'MODBUS_TYPE', 'TCP')
        self.modbus_config.connection_type = modbus_type
        
        if modbus_type == 'TCP':
            tcp_config = getattr(config_loader, 'MODBUS_TCP_CONFIG', {})
            self.modbus_config.server_ip = tcp_config.get('server_ip', '127.0.0.1')
            self.modbus_config.port = tcp_config.get('port', 502)
            self.modbus_config.timeout = tcp_config.get('timeout', 3.0)
        else:  # RTU
            rtu_config = getattr(config_loader, 'MODBUS_RTU_CONFIG', {})
            self.modbus_config.device = rtu_config.get('port', '/dev/ttyUSB0')
            self.modbus_config.baudrate = rtu_config.get('baudrate', 9600)
            self.modbus_config.timeout = rtu_config.get('timeout', 3.0)
    
    def _load_system_config(self, config_loader):
        """加载系统配置"""
        timezone_config = getattr(config_loader, 'TIMEZONE_CONFIG', {})
        self.system_config.timezone_offset = timezone_config.get('timezone_offset', '+08')
    
    def _load_oid_mappings(self, config_loader):
        """加载OID映射"""
        self.oid_mappings = []
        
        # 加载系统 OID 映射
        system_mappings = getattr(config_loader, 'SYSTEM_OID_MAPPING', [])
        for mapping_dict in system_mappings:
            oid_mapping = OIDMapping(
                oid=mapping_dict['oid'],
                description=mapping_dict['description'],
                oid_type='system',
                snmp_data_type=mapping_dict.get('snmp_data_type', 'OctetString'),
                system_type=mapping_dict.get('type'),
                system_value=mapping_dict.get('value')
            )
            self.oid_mappings.append(oid_mapping)
        
        # 加载 SNMP OID 映射 (Modbus)
        snmp_mappings = getattr(config_loader, 'SNMP_OID_MAPPING', [])
        for mapping_dict in snmp_mappings:
            # 获取 Modbus 配置
            modbus_config = mapping_dict.get('modbus_config', {})
            data_processing = mapping_dict.get('data_processing', {})
            
            oid_mapping = OIDMapping(
                oid=mapping_dict['oid'],
                description=mapping_dict['description'],
                oid_type='modbus',
                snmp_data_type=mapping_dict.get('snmp_data_type', 'OctetString'),
                register_address=modbus_config.get('register_address'),
                unit_id=modbus_config.get('unit_id', 1),
                function_code=modbus_config.get('function_code', 3),
                data_type=modbus_config.get('data_type', 'uint16'),
                processing_type=data_processing.get('type', 'direct'),
                coefficient=data_processing.get('coefficient', 1.0),
                offset=data_processing.get('offset', 0.0),
                decimal_places=data_processing.get('decimal_places', 0)
            )
            self.oid_mappings.append(oid_mapping)
        
        logger.info(f"加载OID映射: 系统{len(system_mappings)}个, Modbus{len(snmp_mappings)}个")
    
    async def _on_config_reload(self):
        """配置重载回调"""
        try:
            logger.info("🔄 开始重载配置...")
            
            # 重新加载配置
            await self.load_config()
            
            # 更新统计信息
            self.reload_count += 1
            import time
            self.last_reload_time = time.time()
            
            # 调用所有注册的回调
            for callback in self.reload_callbacks:
                try:
                    await callback(self)
                except Exception as e:
                    logger.error(f"配置重载回调失败: {e}")
            
            logger.info(f"✅ 配置重载完成! 重载次数: {self.reload_count}")
            
        except Exception as e:
            logger.error(f"❌ 配置重载失败: {e}")
    
    def register_reload_callback(self, callback: Callable):
        """注册重载回调"""
        self.reload_callbacks.append(callback)
    
    async def start_monitoring(self):
        """启动配置监控"""
        await self.monitor.start_monitoring()
    
    async def stop(self):
        """停止配置管理器"""
        self.monitor.stop_monitoring()
    
    def get_reload_stats(self) -> Dict[str, Any]:
        """获取重载统计信息"""
        return {
            'reload_count': self.reload_count,
            'last_reload_time': self.last_reload_time,
            'monitor_stats': self.monitor.get_stats()
        }

# 为兼容性添加简化的配置监控器
class SimpleConfigMonitor:
    """简化的配置文件监控器（兼容现有接口）"""
    
    def __init__(self, config_file: str = 'config.ini', check_interval: float = 3.0):
        self.config_file = Path(config_file)
        self.check_interval = check_interval
        self.last_hash = None
        self.last_modified = None
        self.is_monitoring = False
        self.reload_callback = None
        
        # 计算初始哈希值
        self._update_file_info()
        
        logger.info(f"初始化简化配置文件监控器: {self.config_file}")
    
    def _update_file_info(self):
        """更新文件信息"""
        try:
            if self.config_file.exists():
                # 计算文件哈希值
                with open(self.config_file, 'rb') as f:
                    content = f.read()
                    self.last_hash = hashlib.md5(content).hexdigest()
                
                # 获取修改时间
                self.last_modified = self.config_file.stat().st_mtime
                logger.debug(f"配置文件信息更新: hash={self.last_hash[:8]}, mtime={self.last_modified}")
            else:
                logger.warning(f"配置文件不存在: {self.config_file}")
        except Exception as e:
            logger.error(f"获取配置文件信息失败: {e}")
    
    def has_changed(self) -> bool:
        """检查文件是否有变化"""
        try:
            if not self.config_file.exists():
                return False
            
            # 检查修改时间
            current_modified = self.config_file.stat().st_mtime
            if current_modified != self.last_modified:
                # 再检查哈希值
                with open(self.config_file, 'rb') as f:
                    content = f.read()
                    current_hash = hashlib.md5(content).hexdigest()
                
                if current_hash != self.last_hash:
                    logger.info(f"🔄 检测到配置文件变化: {self.config_file}")
                    old_hash = self.last_hash[:8] if self.last_hash else "None"
                    logger.debug(f"哈希值变化: {old_hash} -> {current_hash[:8]}")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查配置文件变化失败: {e}")
            return False
    
    def set_reload_callback(self, callback):
        """设置重载回调函数"""
        self.reload_callback = callback
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        logger.info(f"✅ 开始监控配置文件变化 (检查间隔: {self.check_interval}秒)")
        
        while self.is_monitoring:
            try:
                if self.has_changed():
                    logger.info("🔄 检测到配置文件更改，触发热重载...")
                    
                    # 等待一小段时间，确保文件写入完成
                    await asyncio.sleep(0.5)
                    
                    # 更新文件信息
                    self._update_file_info()
                    
                    # 调用重载回调
                    if self.reload_callback:
                        try:
                            await self.reload_callback()
                        except Exception as e:
                            logger.error(f"配置重载回调失败: {e}")
                
                await asyncio.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"监控配置文件异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    def stop_monitoring(self):
        """停止监控"""
        if self.is_monitoring:
            self.is_monitoring = False
            logger.info("✅ 停止监控配置文件变化")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        return {
            'config_file': str(self.config_file),
            'is_monitoring': self.is_monitoring,
            'check_interval': self.check_interval,
            'last_hash': self.last_hash[:8] if self.last_hash else None,
            'last_modified': self.last_modified,
            'file_exists': self.config_file.exists()
        }