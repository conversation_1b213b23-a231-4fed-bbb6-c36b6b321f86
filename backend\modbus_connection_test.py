#!/usr/bin/env python3
"""
Modbus连接诊断工具

用于诊断和测试Modbus连接问题，帮助快速定位连接中断的原因。

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import asyncio
import logging
import sys
import socket
import time
from typing import Dict, Any

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_network_connectivity(host: str, port: int, timeout: float = 3.0) -> bool:
    """测试网络连通性"""
    try:
        logger.info(f"🔍 测试网络连通性: {host}:{port}")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            logger.info(f"✅ 网络连通性正常: {host}:{port}")
            return True
        else:
            logger.error(f"❌ 网络连接失败: {host}:{port} (错误码: {result})")
            return False
            
    except Exception as e:
        logger.error(f"❌ 网络测试异常: {e}")
        return False

async def test_modbus_connection():
    """测试Modbus连接"""
    try:
        logger.info("🔍 测试Modbus连接...")
        
        from core.modbus_client import AsyncModbusConnectionPool, ConnectionConfig
        from utils.config_loader import get_config
        
        # 获取配置
        config = get_config()
        modbus_type = config.get('modbus_type', 'TCP')
        
        if modbus_type == 'TCP':
            tcp_config = config['modbus_tcp']
            logger.info(f"📋 Modbus TCP配置: {tcp_config}")
            
            # 先测试网络连通性
            if not test_network_connectivity(
                tcp_config['server_ip'], 
                tcp_config['port'], 
                timeout=5.0
            ):
                return False
            
            # 创建连接配置
            config_obj = ConnectionConfig(
                connection_type='TCP',
                host=tcp_config['server_ip'],
                port=tcp_config['port'],
                timeout=tcp_config.get('timeout', 3)
            )
            
            # 创建连接池
            modbus_pool = AsyncModbusConnectionPool(config_obj)
            
        else:  # RTU
            rtu_config = config['modbus_rtu']
            logger.info(f"📋 Modbus RTU配置: {rtu_config}")
            
            # 创建连接配置
            config_obj = ConnectionConfig(
                connection_type='RTU',
                device=rtu_config['port'],
                baudrate=rtu_config.get('baudrate', 9600),
                timeout=rtu_config.get('timeout', 3)
            )
            
            modbus_pool = AsyncModbusConnectionPool(config_obj)
        
        # 启动连接池
        await modbus_pool.start()
        
        if await modbus_pool.check_connection_status():
            logger.info("✅ Modbus连接池启动成功")
            
            # 测试具体的OID读取
            await test_specific_oid(modbus_pool)
            
        else:
            logger.error("❌ Modbus连接池启动失败")
            return False
        
        # 清理
        await modbus_pool.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Modbus连接测试失败: {e}")
        return False

async def test_specific_oid(modbus_pool):
    """测试具体的OID读取"""
    try:
        logger.info("🔍 测试问题OID: .*******.4.1.41475.********.*******")
        
        # 温度传感器配置
        test_configs = [
            {
                'name': 'temperature_sensor_1',
                'address': 0x100,  # 256
                'unit_id': 1,
                'function_code': 3
            },
            {
                'name': 'humidity_sensor_1', 
                'address': 0x101,  # 257
                'unit_id': 1,
                'function_code': 3
            },
            {
                'name': 'device_operation_mode',
                'address': 0x102,  # 258
                'unit_id': 1,
                'function_code': 3
            }
        ]
        
        for config in test_configs:
            logger.info(f"📊 测试 {config['name']} (地址=0x{config['address']:X}, 单元ID={config['unit_id']})")
            
            try:
                result = await modbus_pool.read_register(
                    address=config['address'],
                    unit_id=config['unit_id'],
                    function_code=config['function_code'],
                    count=1
                )
                
                if result is not None:
                    logger.info(f"✅ 读取成功: {config['name']} = {result}")
                else:
                    logger.error(f"❌ 读取失败: {config['name']}")
                    
            except Exception as e:
                logger.error(f"❌ 读取异常: {config['name']} - {e}")
        
    except Exception as e:
        logger.error(f"❌ OID测试异常: {e}")

def print_diagnostic_suggestions():
    """打印诊断建议"""
    logger.info("🔧 连接故障排查建议:")
    logger.info("=" * 60)
    logger.info("1. 检查Modbus设备状态:")
    logger.info("   - 设备是否正常开机")
    logger.info("   - 电源和网络连接是否稳定")
    logger.info("   - 设备IP地址是否正确")
    
    logger.info("\n2. 检查网络配置:")
    logger.info("   - ping 127.0.0.1")
    logger.info("   - telnet 127.0.0.1 502")
    logger.info("   - 检查防火墙设置")
    
    logger.info("\n3. 检查Modbus配置:")
    logger.info("   - 寄存器地址是否正确")
    logger.info("   - 单元ID是否匹配")
    logger.info("   - 功能码是否支持")
    
    logger.info("\n4. 尝试其他工具:")
    logger.info("   - 使用Modbus Poll等工具测试")
    logger.info("   - 检查设备文档")
    
    logger.info("\n5. 配置修改建议:")
    logger.info("   - 增加timeout时间")
    logger.info("   - 调整retry_interval")
    logger.info("   - 检查register_address格式")

async def main():
    """主函数"""
    logger.info("🚀 开始Modbus连接诊断")
    logger.info("=" * 60)
    
    try:
        # 测试Modbus连接
        success = await test_modbus_connection()
        
        logger.info("=" * 60)
        if success:
            logger.info("🎉 Modbus连接诊断完成")
        else:
            logger.error("❌ Modbus连接存在问题")
            print_diagnostic_suggestions()
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 诊断过程异常: {e}")
        print_diagnostic_suggestions()
        return False

if __name__ == '__main__':
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("用户中断诊断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"诊断执行异常: {e}")
        sys.exit(1)