<template>
  <div class="snmp-modbus-config">
    <div class="page-title">
      <el-icon><Setting /></el-icon>
      SNMP-Modbus 桥接配置
    </div>

    <el-row :gutter="20">
      <!-- 左侧配置面板 -->
      <el-col :span="16">
        <div class="config-panels">
          <!-- SNMP Bridge 基础配置 -->
          <el-card class="config-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Network /></el-icon>
                <span>SNMP Bridge 基础配置</span>
              </div>
            </template>
            
            <el-form :model="bridgeConfig" label-width="140px" size="default">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="监听IP地址">
                    <el-input v-model="bridgeConfig.listen_ip" placeholder="0.0.0.0" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="监听端口">
                    <el-input-number 
                      v-model="bridgeConfig.listen_port" 
                      :min="1" 
                      :max="65535"
                      controls-position="right"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="社区字符串">
                    <el-input v-model="bridgeConfig.community" placeholder="public" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="时区偏移">
                    <el-select v-model="bridgeConfig.timezone_offset" style="width: 100%">
                      <el-option label="UTC+08" value="+08" />
                      <el-option label="UTC+07" value="+07" />
                      <el-option label="UTC+06" value="+06" />
                      <el-option label="UTC+05" value="+05" />
                      <el-option label="UTC+00" value="+00" />
                      <el-option label="UTC-05" value="-05" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>

          <!-- Modbus 配置 -->
          <el-card class="config-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Connection /></el-icon>
                <span>Modbus 连接配置</span>
              </div>
            </template>
            
            <el-form :model="modbusConfig" label-width="140px" size="default">
              <el-form-item label="Modbus模式">
                <el-radio-group v-model="modbusConfig.type" @change="handleModbusTypeChange">
                  <el-radio-button label="TCP">TCP模式</el-radio-button>
                  <el-radio-button label="RTU">RTU模式</el-radio-button>
                </el-radio-group>
              </el-form-item>

              <!-- TCP 模式配置 -->
              <div v-if="modbusConfig.type === 'TCP'" class="modbus-tcp-config">
                <el-divider content-position="left">TCP连接参数</el-divider>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="服务器IP">
                      <el-input v-model="modbusConfig.tcp.server_ip" placeholder="127.0.0.1" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="端口">
                      <el-input-number 
                        v-model="modbusConfig.tcp.port" 
                        :min="1" 
                        :max="65535"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="超时时间(秒)">
                      <el-input-number 
                        v-model="modbusConfig.tcp.timeout" 
                        :min="1" 
                        :max="60"
                        controls-position="right"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- RTU 模式配置 -->
              <div v-if="modbusConfig.type === 'RTU'" class="modbus-rtu-config">
                <el-divider content-position="left">RTU串口参数</el-divider>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="串口设备">
                      <el-select v-model="modbusConfig.rtu.port" style="width: 100%" filterable allow-create>
                        <el-option label="/dev/ttyUSB0" value="/dev/ttyUSB0" />
                        <el-option label="/dev/ttyUSB1" value="/dev/ttyUSB1" />
                        <el-option label="COM1" value="COM1" />
                        <el-option label="COM2" value="COM2" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="波特率">
                      <el-select v-model="modbusConfig.rtu.baudrate" style="width: 100%">
                        <el-option label="9600" :value="9600" />
                        <el-option label="19200" :value="19200" />
                        <el-option label="38400" :value="38400" />
                        <el-option label="115200" :value="115200" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-form>
          </el-card>

          <!-- OID 映射管理 -->
          <el-card class="config-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Document /></el-icon>
                <span>OID 映射管理</span>
                <div class="header-actions">
                  <el-button type="primary" size="small" @click="showAddOIDDialog = true">
                    <el-icon><Plus /></el-icon>
                    添加OID
                  </el-button>
                </div>
              </div>
            </template>
            
            <el-table :data="oidMappings" stripe style="width: 100%">
              <el-table-column prop="oid" label="OID" width="300" />
              <el-table-column prop="description" label="描述" width="200" />
              <el-table-column prop="type" label="类型" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.type === 'system' ? 'success' : 'primary'" size="small">
                    {{ row.type === 'system' ? '系统' : 'Modbus' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row, $index }">
                  <el-button type="primary" size="small" text @click="editOID(row, $index)">
                    编辑
                  </el-button>
                  <el-popconfirm
                    title="确定要删除这个OID映射吗？"
                    @confirm="deleteOID($index)"
                  >
                    <template #reference>
                      <el-button type="danger" size="small" text>
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>

      <!-- 右侧操作面板 -->
      <el-col :span="8">
        <div class="action-panels">
          <!-- 操作按钮 -->
          <el-card class="action-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Operation /></el-icon>
                <span>配置操作</span>
              </div>
            </template>
            
            <div class="action-buttons">
              <el-button type="primary" @click="saveConfig" :loading="saving" style="width: 100%; margin-bottom: 10px;">
                保存配置
              </el-button>
              <el-button @click="loadConfig" :loading="loading" style="width: 100%; margin-bottom: 10px;">
                重载配置
              </el-button>
              <el-button type="success" @click="testConnection" :loading="testing" style="width: 100%; margin-bottom: 10px;">
                测试连接
              </el-button>
              <el-button type="warning" @click="restartService" :loading="restarting" style="width: 100%;">
                重启服务
              </el-button>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 添加/编辑 OID 对话框 -->
    <el-dialog
      v-model="showAddOIDDialog"
      :title="editingOIDIndex !== null ? '编辑OID映射' : '添加OID映射'"
      width="600px"
    >
      <el-form :model="newOID" label-width="120px">
        <el-form-item label="OID">
          <el-input v-model="newOID.oid" placeholder="如: .*******.4.1.41475.********.1.2.0" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="newOID.description" placeholder="OID的功能描述" />
        </el-form-item>
        <el-form-item label="类型">
          <el-radio-group v-model="newOID.type">
            <el-radio label="system">系统固定值</el-radio>
            <el-radio label="modbus">Modbus地址</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 系统固定值配置 -->
        <div v-if="newOID.type === 'system'">
          <el-form-item label="固定值">
            <el-input v-model="newOID.value" placeholder="返回的固定值" />
          </el-form-item>
        </div>

        <!-- Modbus地址配置 -->
        <div v-if="newOID.type === 'modbus'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="寄存器地址">
                <el-input-number 
                  v-model="newOID.address" 
                  :min="0" 
                  :max="65535"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="设备ID">
                <el-input-number 
                  v-model="newOID.unitId" 
                  :min="1" 
                  :max="255"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelAddOID">取消</el-button>
          <el-button type="primary" @click="confirmAddOID">
            {{ editingOIDIndex !== null ? '更新' : '添加' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Network, Connection, Document, Plus, Operation } from '@element-plus/icons-vue'
import api from '@/utils/api'

// 配置数据
const bridgeConfig = reactive({
  listen_ip: '0.0.0.0',
  listen_port: 1161,
  community: 'public',
  timezone_offset: '+08'
})

const modbusConfig = reactive({
  type: 'TCP',
  tcp: {
    server_ip: '127.0.0.1',
    port: 502,
    timeout: 3
  },
  rtu: {
    port: '/dev/ttyUSB0',
    baudrate: 9600
  }
})

// OID 映射数据
const oidMappings = ref([])
const showAddOIDDialog = ref(false)
const editingOIDIndex = ref(null)

const newOID = reactive({
  oid: '',
  description: '',
  type: 'system',
  value: '',
  address: 0,
  unitId: 1
})

// 状态数据
const loading = ref(false)
const saving = ref(false)
const testing = ref(false)
const restarting = ref(false)

// 方法
const handleModbusTypeChange = () => {
  // 处理模式切换
}

const loadConfig = async () => {
  loading.value = true
  try {
    const response = await api.get('/api/config')
    // 加载配置逻辑
    ElMessage.success('配置加载完成')
  } catch (error) {
    ElMessage.error('配置加载失败')
  } finally {
    loading.value = false
  }
}

const saveConfig = async () => {
  saving.value = true
  try {
    // 保存配置逻辑
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  testing.value = true
  try {
    // 测试连接逻辑
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

const restartService = async () => {
  restarting.value = true
  try {
    await api.post('/api/bridge/control', { action: 'restart' })
    ElMessage.success('服务重启成功')
  } catch (error) {
    ElMessage.error('服务重启失败')
  } finally {
    restarting.value = false
  }
}

const editOID = (row, index) => {
  editingOIDIndex.value = index
  Object.assign(newOID, row)
  showAddOIDDialog.value = true
}

const deleteOID = (index) => {
  oidMappings.value.splice(index, 1)
  ElMessage.success('删除成功')
}

const confirmAddOID = () => {
  if (editingOIDIndex.value !== null) {
    Object.assign(oidMappings.value[editingOIDIndex.value], newOID)
  } else {
    oidMappings.value.push({ ...newOID })
  }
  cancelAddOID()
  ElMessage.success(editingOIDIndex.value !== null ? '更新成功' : '添加成功')
}

const cancelAddOID = () => {
  showAddOIDDialog.value = false
  editingOIDIndex.value = null
  Object.assign(newOID, {
    oid: '',
    description: '',
    type: 'system',
    value: '',
    address: 0,
    unitId: 1
  })
}

onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.snmp-modbus-config {
  padding: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #409eff;
}

.page-title .el-icon {
  margin-right: 10px;
}

.config-card, .action-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header span {
  margin-left: 8px;
  font-weight: bold;
}

.action-buttons .el-button {
  margin-bottom: 10px;
}

.action-buttons .el-button:last-child {
  margin-bottom: 0;
}
</style>