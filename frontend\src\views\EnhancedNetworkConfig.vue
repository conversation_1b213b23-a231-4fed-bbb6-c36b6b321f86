<template>
  <div class="network-config">
    <div class="page-title">
      <el-icon><Connection /></el-icon>
      Linux 网络接口配置
    </div>

    <el-row :gutter="20">
      <!-- 网络接口列表 -->
      <el-col :span="16">
        <el-card class="interface-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>网络接口</span>
              <div class="header-actions">
                <el-button type="primary" size="small" @click="refreshInterfaces" :loading="loading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <el-table :data="interfaces" stripe style="width: 100%">
            <el-table-column prop="name" label="接口名称" width="120">
              <template #default="{ row }">
                <el-tag :type="row.state === 'UP' ? 'success' : 'info'" size="small">
                  {{ row.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="state" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.state === 'UP' ? 'success' : 'danger'" size="small">
                  {{ row.state }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="ip" label="IP地址" width="150" />
            <el-table-column prop="netmask" label="子网掩码" width="150" />
            <el-table-column prop="type" label="配置类型" width="100">
              <template #default="{ row }">
                <el-tag :type="row.type === 'static' ? 'primary' : 'warning'" size="small">
                  {{ row.type === 'static' ? '静态' : 'DHCP' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button type="primary" size="small" text @click="editInterface(row)">
                  配置
                </el-button>
                <el-button 
                  :type="row.state === 'UP' ? 'warning' : 'success'" 
                  size="small" 
                  text 
                  @click="toggleInterface(row)"
                >
                  {{ row.state === 'UP' ? '停用' : '启用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 网关和DNS配置 -->
        <el-card class="gateway-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Position /></el-icon>
              <span>网关和DNS配置</span>
            </div>
          </template>
          
          <el-form :model="gatewayConfig" label-width="120px">
            <el-form-item label="默认网关">
              <el-input v-model="gatewayConfig.gateway" placeholder="***********" style="width: 300px;" />
              <el-button type="primary" @click="setGateway" style="margin-left: 10px;">设置</el-button>
            </el-form-item>
            <el-form-item label="主DNS服务器">
              <el-input v-model="gatewayConfig.dns1" placeholder="8.8.8.8" style="width: 300px;" />
            </el-form-item>
            <el-form-item label="备用DNS服务器">
              <el-input v-model="gatewayConfig.dns2" placeholder="8.8.4.4" style="width: 300px;" />
              <el-button type="primary" @click="setDNS" style="margin-left: 10px;">设置DNS</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧状态面板 -->
      <el-col :span="8">
        <div class="status-panels">
          <!-- 网络状态 -->
          <el-card class="status-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Monitor /></el-icon>
                <span>网络状态</span>
              </div>
            </template>
            
            <div class="status-content">
              <div class="status-item">
                <span class="status-label">活动接口:</span>
                <el-text type="primary">{{ activeInterfaces }}</el-text>
              </div>
              <div class="status-item">
                <span class="status-label">主要IP:</span>
                <el-text>{{ primaryIP }}</el-text>
              </div>
              <div class="status-item">
                <span class="status-label">默认网关:</span>
                <el-text>{{ gatewayConfig.gateway || '未设置' }}</el-text>
              </div>
              <div class="status-item">
                <span class="status-label">DNS服务器:</span>
                <el-text>{{ gatewayConfig.dns1 || '未设置' }}</el-text>
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card class="quick-actions-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Operation /></el-icon>
                <span>快速操作</span>
              </div>
            </template>
            
            <div class="quick-actions">
              <el-button type="success" @click="pingTest" :loading="pinging" style="width: 100%; margin-bottom: 10px;">
                <el-icon><Connection /></el-icon>
                网络连通性测试
              </el-button>
              <el-button type="warning" @click="restartNetwork" :loading="restarting" style="width: 100%; margin-bottom: 10px;">
                <el-icon><RefreshRight /></el-icon>
                重启网络服务
              </el-button>
              <el-button @click="exportConfig" style="width: 100%;">
                <el-icon><Download /></el-icon>
                导出网络配置
              </el-button>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 编辑接口对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="`配置网络接口 - ${editingInterface?.name}`"
      width="600px"
    >
      <el-form :model="interfaceForm" label-width="120px">
        <el-form-item label="配置类型">
          <el-radio-group v-model="interfaceForm.type">
            <el-radio-button label="static">静态IP</el-radio-button>
            <el-radio-button label="dhcp">DHCP自动获取</el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <!-- 静态IP配置 -->
        <div v-if="interfaceForm.type === 'static'">
          <el-form-item label="IP地址" required>
            <el-input v-model="interfaceForm.ip" placeholder="***********00" />
          </el-form-item>
          <el-form-item label="子网掩码" required>
            <el-select v-model="interfaceForm.netmask" style="width: 100%">
              <el-option label="************* (/24)" value="*************" />
              <el-option label="*********** (/16)" value="***********" />
              <el-option label="********* (/8)" value="*********" />
              <el-option label="************* (/23)" value="*************" />
              <el-option label="************* (/22)" value="*************" />
            </el-select>
          </el-form-item>
          <el-form-item label="网关">
            <el-input v-model="interfaceForm.gateway" placeholder="***********" />
          </el-form-item>
        </div>

        <!-- 高级选项 -->
        <el-form-item>
          <el-checkbox v-model="interfaceForm.autoConnect">开机自动连接</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveInterface" :loading="saving">
            保存配置
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 网络测试结果对话框 -->
    <el-dialog v-model="showPingDialog" title="网络连通性测试" width="500px">
      <div class="ping-results">
        <el-text type="info">测试目标: 8.8.8.8, 114.114.114.114</el-text>
        <el-divider />
        <div v-for="result in pingResults" :key="result.target" class="ping-item">
          <div class="ping-target">
            <el-tag :type="result.success ? 'success' : 'danger'" size="small">
              {{ result.target }}
            </el-tag>
            <span class="ping-status">{{ result.success ? '连通' : '不通' }}</span>
          </div>
          <div v-if="result.success" class="ping-details">
            <el-text size="small">延迟: {{ result.latency }}ms</el-text>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Connection, Monitor, Position, Operation, Refresh, RefreshRight,
  Download
} from '@element-plus/icons-vue'
import api from '@/utils/api'

// 数据
const interfaces = ref([])
const loading = ref(false)
const saving = ref(false)
const pinging = ref(false)
const restarting = ref(false)

const showEditDialog = ref(false)
const showPingDialog = ref(false)
const editingInterface = ref(null)

const gatewayConfig = reactive({
  gateway: '',
  dns1: '',
  dns2: ''
})

const interfaceForm = reactive({
  name: '',
  type: 'static',
  ip: '',
  netmask: '*************',
  gateway: '',
  autoConnect: true
})

const pingResults = ref([])

// 计算属性
const activeInterfaces = computed(() => {
  return interfaces.value.filter(iface => iface.state === 'UP').length
})

const primaryIP = computed(() => {
  const primary = interfaces.value.find(iface => iface.state === 'UP' && iface.ip)
  return primary?.ip || '未配置'
})

// 方法
const refreshInterfaces = async () => {
  loading.value = true
  try {
    const response = await api.get('/api/network/interfaces')
    interfaces.value = response.data.data || []
    ElMessage.success('网络接口信息已刷新')
  } catch (error) {
    ElMessage.error('获取网络接口失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const editInterface = (iface) => {
  editingInterface.value = iface
  Object.assign(interfaceForm, {
    name: iface.name,
    type: iface.type || 'static',
    ip: iface.ip || '',
    netmask: iface.netmask || '*************',
    gateway: iface.gateway || '',
    autoConnect: true
  })
  showEditDialog.value = true
}

const saveInterface = async () => {
  saving.value = true
  try {
    await api.post('/api/network/configure', {
      interface: interfaceForm.name,
      ...interfaceForm
    })
    
    ElMessage.success('网络接口配置已保存')
    showEditDialog.value = false
    await refreshInterfaces()
  } catch (error) {
    ElMessage.error('配置保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const cancelEdit = () => {
  showEditDialog.value = false
  editingInterface.value = null
}

const toggleInterface = async (iface) => {
  try {
    const action = iface.state === 'UP' ? 'down' : 'up'
    await api.post('/api/network/interface-control', {
      interface: iface.name,
      action
    })
    
    ElMessage.success(`接口${iface.name}已${action === 'up' ? '启用' : '停用'}`)
    await refreshInterfaces()
  } catch (error) {
    ElMessage.error('接口状态切换失败: ' + error.message)
  }
}

const setGateway = async () => {
  try {
    await api.post('/api/network/gateway', {
      gateway: gatewayConfig.gateway
    })
    ElMessage.success('默认网关设置成功')
  } catch (error) {
    ElMessage.error('网关设置失败: ' + error.message)
  }
}

const setDNS = async () => {
  try {
    await api.post('/api/network/dns', {
      dns1: gatewayConfig.dns1,
      dns2: gatewayConfig.dns2
    })
    ElMessage.success('DNS设置成功')
  } catch (error) {
    ElMessage.error('DNS设置失败: ' + error.message)
  }
}

const pingTest = async () => {
  pinging.value = true
  try {
    const response = await api.post('/api/network/ping-test')
    pingResults.value = response.data.data || []
    showPingDialog.value = true
  } catch (error) {
    ElMessage.error('网络测试失败: ' + error.message)
  } finally {
    pinging.value = false
  }
}

const restartNetwork = async () => {
  try {
    await ElMessageBox.confirm(
      '重启网络服务可能会暂时中断连接，确定要继续吗？',
      '确认重启',
      { type: 'warning' }
    )
    
    restarting.value = true
    await api.post('/api/network/restart')
    ElMessage.success('网络服务重启成功')
    
    // 延迟刷新
    setTimeout(() => {
      refreshInterfaces()
    }, 3000)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('网络服务重启失败: ' + error.message)
    }
  } finally {
    restarting.value = false
  }
}

const exportConfig = async () => {
  try {
    const response = await api.get('/api/network/export', { responseType: 'blob' })
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = 'network-config.json'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('网络配置导出成功')
  } catch (error) {
    ElMessage.error('配置导出失败: ' + error.message)
  }
}

onMounted(() => {
  refreshInterfaces()
})
</script>

<style scoped>
.network-config {
  padding: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #409eff;
}

.page-title .el-icon {
  margin-right: 10px;
}

.interface-card, .gateway-card, .status-card, .quick-actions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header span {
  margin-left: 8px;
  font-weight: bold;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: #606266;
}

.quick-actions .el-button {
  margin-bottom: 10px;
}

.quick-actions .el-button:last-child {
  margin-bottom: 0;
}

.ping-results {
  padding: 10px 0;
}

.ping-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 10px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.ping-target {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ping-status {
  font-weight: 500;
}

.ping-details {
  margin-left: 20px;
}
</style>