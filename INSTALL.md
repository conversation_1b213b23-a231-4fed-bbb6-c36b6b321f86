# 安装指南

本文档详细说明如何在Debian/Ubuntu系统上安装和配置SNMP-Modbus Bridge Web配置管理工具。

## 📋 系统要求

### 支持的操作系统
- Ubuntu 18.04 LTS 或更高版本
- Debian 10 或更高版本

### 硬件要求
- **内存**: 最小 1GB，推荐 2GB
- **存储**: 最小 2GB 可用空间
- **网络**: 需要互联网连接下载依赖

### 软件依赖
- Python 3.7 或更高版本
- Node.js 16 或更高版本
- Nginx
- Supervisor

## 🔧 环境准备

### 1. 更新系统包
```bash
sudo apt-get update
sudo apt-get upgrade -y
```

### 2. 安装基础依赖
```bash
sudo apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    python3 \
    python3-pip \
    python3-venv \
    net-tools \
    iproute2
```

### 3. 安装Node.js
```bash
# 安装Node.js 18 LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 4. 安装Nginx
```bash
sudo apt-get install -y nginx

# 启用Nginx服务
sudo systemctl enable nginx
sudo systemctl start nginx

# 验证安装
sudo systemctl status nginx
```

### 5. 安装Supervisor
```bash
sudo apt-get install -y supervisor

# 启用Supervisor服务
sudo systemctl enable supervisor
sudo systemctl start supervisor

# 验证安装
sudo systemctl status supervisor
```

## 📦 快速安装

### 自动安装（推荐）

1. **下载项目**
```bash
# 如果有Git仓库
git clone <repository-url>
cd snmp-modbus-bridge/web-config-manager

# 或手动下载并解压项目文件
```

2. **运行自动安装脚本**
```bash
chmod +x deploy.sh
./deploy.sh
```

自动安装脚本将：
- 检查系统要求
- 安装必要的系统依赖
- 创建项目目录
- 复制项目文件
- 安装Python和Node.js依赖
- 配置Supervisor和Nginx
- 启动所有服务

### 手动安装

如果自动安装遇到问题，可以按照以下步骤手动安装：

#### 步骤1: 创建项目目录
```bash
sudo mkdir -p /opt/snmp-modbus-bridge/web-config-manager
sudo chown -R $USER:$USER /opt/snmp-modbus-bridge
```

#### 步骤2: 复制项目文件
```bash
cp -r backend /opt/snmp-modbus-bridge/web-config-manager/
cp -r frontend /opt/snmp-modbus-bridge/web-config-manager/
```

#### 步骤3: 安装后端依赖
```bash
cd /opt/snmp-modbus-bridge/web-config-manager/backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -r requirements.txt
```

#### 步骤4: 安装前端依赖并构建
```bash
cd /opt/snmp-modbus-bridge/web-config-manager/frontend

# 安装依赖
npm install

# 构建生产版本
npm run build
```

#### 步骤5: 配置Supervisor
```bash
sudo tee /etc/supervisor/conf.d/snmp-modbus-bridge-web.conf > /dev/null <<EOF
[program:snmp-modbus-bridge-backend]
command=/opt/snmp-modbus-bridge/web-config-manager/backend/venv/bin/python /opt/snmp-modbus-bridge/web-config-manager/backend/main.py
directory=/opt/snmp-modbus-bridge/web-config-manager/backend
user=$USER
autostart=true
autorestart=true
stdout_logfile=/var/log/snmp-modbus-bridge/backend.log
stderr_logfile=/var/log/snmp-modbus-bridge/backend_error.log
environment=PATH="/opt/snmp-modbus-bridge/web-config-manager/backend/venv/bin"

[group:snmp-modbus-bridge-web]
programs=snmp-modbus-bridge-backend
priority=999
EOF

# 创建日志目录
sudo mkdir -p /var/log/snmp-modbus-bridge
sudo chown -R $USER:$USER /var/log/snmp-modbus-bridge

# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update
```

#### 步骤6: 配置Nginx
```bash
sudo tee /etc/nginx/sites-available/snmp-modbus-bridge-web > /dev/null <<EOF
server {
    listen 80;
    server_name localhost;

    # 前端静态文件
    location / {
        root /opt/snmp-modbus-bridge/web-config-manager/frontend/dist;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        root /opt/snmp-modbus-bridge/web-config-manager/frontend/dist;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/snmp-modbus-bridge-web /etc/nginx/sites-enabled/

# 删除默认站点（可选）
sudo rm -f /etc/nginx/sites-enabled/default

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

#### 步骤7: 启动服务
```bash
# 启动后端服务
sudo supervisorctl start snmp-modbus-bridge-backend

# 确保Nginx运行
sudo systemctl start nginx
```

## ✅ 安装验证

### 1. 检查服务状态
```bash
# 检查Supervisor服务
sudo supervisorctl status

# 检查Nginx服务
sudo systemctl status nginx

# 检查端口监听
netstat -ln | grep -E ":(80|8000)"
```

### 2. 访问测试
```bash
# 测试后端API
curl http://localhost:8000/health

# 测试前端页面
curl -I http://localhost
```

### 3. Web界面访问
打开浏览器访问：
- **Web界面**: http://localhost 或 http://服务器IP
- **API文档**: http://localhost:8000/docs

## 🔧 配置调优

### 1. 内存优化
对于内存较小的系统，可以调整以下配置：

```bash
# 编辑Supervisor配置
sudo nano /etc/supervisor/conf.d/snmp-modbus-bridge-web.conf

# 添加内存限制
environment=PATH="/opt/snmp-modbus-bridge/web-config-manager/backend/venv/bin",PYTHONUNBUFFERED=1
```

### 2. 日志轮转
配置日志轮转防止日志文件过大：

```bash
sudo tee /etc/logrotate.d/snmp-modbus-bridge > /dev/null <<EOF
/var/log/snmp-modbus-bridge/*.log {
    daily
    missingok
    rotate 7
    compress
    notifempty
    copytruncate
}
EOF
```

### 3. 防火墙配置
如果启用了防火墙，需要开放相应端口：

```bash
# UFW防火墙
sudo ufw allow 80/tcp
sudo ufw allow 8000/tcp

# iptables防火墙
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
```

## 🚨 故障排除

### 常见安装问题

#### 1. Node.js版本过低
```bash
# 卸载旧版本
sudo apt-get remove nodejs npm

# 重新安装最新版本
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### 2. Python虚拟环境创建失败
```bash
# 安装python3-venv
sudo apt-get install python3-venv

# 如果还是失败，尝试使用virtualenv
pip3 install virtualenv
virtualenv -p python3 venv
```

#### 3. npm安装依赖失败
```bash
# 清理npm缓存
npm cache clean --force

# 使用淘宝镜像
npm config set registry https://registry.npm.taobao.org

# 重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 4. 权限问题
```bash
# 修复项目目录权限
sudo chown -R $USER:$USER /opt/snmp-modbus-bridge

# 修复日志目录权限
sudo chown -R $USER:$USER /var/log/snmp-modbus-bridge
```

#### 5. 端口被占用
```bash
# 检查端口占用
sudo netstat -tlnp | grep -E ":(80|8000)"

# 停止占用端口的服务
sudo systemctl stop apache2  # 如果Apache占用80端口
```

### 服务启动问题

#### 1. 后端服务启动失败
```bash
# 查看详细错误日志
sudo tail -f /var/log/snmp-modbus-bridge/backend_error.log

# 手动测试启动
cd /opt/snmp-modbus-bridge/web-config-manager/backend
source venv/bin/activate
python main.py
```

#### 2. Nginx配置错误
```bash
# 测试Nginx配置
sudo nginx -t

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log
```

## 🔄 卸载指南

如果需要完全卸载Web配置管理工具：

```bash
# 停止服务
sudo supervisorctl stop snmp-modbus-bridge-backend

# 删除Supervisor配置
sudo rm -f /etc/supervisor/conf.d/snmp-modbus-bridge-web.conf
sudo supervisorctl reread
sudo supervisorctl update

# 删除Nginx配置
sudo rm -f /etc/nginx/sites-enabled/snmp-modbus-bridge-web
sudo rm -f /etc/nginx/sites-available/snmp-modbus-bridge-web
sudo systemctl restart nginx

# 删除项目文件
sudo rm -rf /opt/snmp-modbus-bridge/web-config-manager

# 删除日志文件
sudo rm -rf /var/log/snmp-modbus-bridge

# 可选：卸载系统依赖（如果不被其他应用使用）
# sudo apt-get remove nginx supervisor nodejs
```

## 📞 获取帮助

如果在安装过程中遇到问题：

1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 确保系统满足所有要求
4. 创建GitHub Issue描述具体问题

---

安装完成后，请查看[README.md](README.md)了解如何使用Web配置管理工具。