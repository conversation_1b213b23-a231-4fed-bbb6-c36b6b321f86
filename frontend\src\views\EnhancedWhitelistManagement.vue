<template>
  <div class="whitelist-management">
    <div class="page-title">
      <el-icon><Shield /></el-icon>
      SNMP 访问白名单管理
    </div>

    <el-row :gutter="20">
      <!-- 白名单配置 -->
      <el-col :span="16">
        <el-card class="whitelist-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Lock /></el-icon>
              <span>白名单配置</span>
              <div class="header-actions">
                <el-button type="primary" size="small" @click="showAddDialog = true" :disabled="whitelist.length >= 10">
                  <el-icon><Plus /></el-icon>
                  添加IP
                </el-button>
              </div>
            </div>
          </template>
          
          <div class="whitelist-config">
            <el-form :model="whitelistConfig" label-width="120px" style="margin-bottom: 20px;">
              <el-form-item label="白名单状态">
                <el-switch 
                  v-model="whitelistConfig.enabled" 
                  active-text="启用" 
                  inactive-text="禁用"
                  @change="toggleWhitelist"
                />
                <el-text type="info" style="margin-left: 10px;">
                  {{ whitelistConfig.enabled ? '仅允许白名单内的IP访问' : '允许所有IP访问' }}
                </el-text>
              </el-form-item>
            </el-form>

            <el-table :data="whitelist" stripe style="width: 100%">
              <el-table-column type="index" label="#" width="50" />
              <el-table-column prop="ip" label="IP地址" width="150">
                <template #default="{ row }">
                  <el-text class="ip-address">{{ row.ip }}</el-text>
                </template>
              </el-table-column>
              <el-table-column prop="netmask" label="子网掩码" width="150">
                <template #default="{ row }">
                  <el-text>{{ row.netmask }}</el-text>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="200">
                <template #default="{ row }">
                  <el-input 
                    v-model="row.description" 
                    size="small" 
                    placeholder="输入描述信息"
                    @blur="updateDescription(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="row.enabled ? 'success' : 'info'" size="small">
                    {{ row.enabled ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row, $index }">
                  <el-button 
                    :type="row.enabled ? 'warning' : 'success'" 
                    size="small" 
                    text 
                    @click="toggleEntry(row, $index)"
                  >
                    {{ row.enabled ? '禁用' : '启用' }}
                  </el-button>
                  <el-popconfirm
                    title="确定要删除这个白名单条目吗？"
                    @confirm="deleteEntry($index)"
                  >
                    <template #reference>
                      <el-button type="danger" size="small" text>
                        删除
                      </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>

            <div class="whitelist-footer">
              <el-text type="info">
                白名单最多支持 10 个条目，当前已使用 {{ whitelist.length }} 个
              </el-text>
            </div>
          </div>
        </el-card>

        <!-- IP验证工具 -->
        <el-card class="validation-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Search /></el-icon>
              <span>IP访问验证</span>
            </div>
          </template>
          
          <el-form :model="validationForm" label-width="120px">
            <el-form-item label="测试IP地址">
              <el-row :gutter="10">
                <el-col :span="16">
                  <el-input 
                    v-model="validationForm.testIP" 
                    placeholder="输入要测试的IP地址，如: *************"
                  />
                </el-col>
                <el-col :span="8">
                  <el-button type="primary" @click="validateIP" :loading="validating" style="width: 100%;">
                    验证访问权限
                  </el-button>
                </el-col>
              </el-row>
            </el-form-item>
            
            <div v-if="validationResult" class="validation-result">
              <el-alert
                :type="validationResult.allowed ? 'success' : 'error'"
                :title="validationResult.allowed ? '允许访问' : '拒绝访问'"
                :description="validationResult.message"
                show-icon
                :closable="false"
              />
            </div>
          </el-form>
        </el-card>
      </el-col>

      <!-- 右侧状态面板 -->
      <el-col :span="8">
        <div class="status-panels">
          <!-- 访问统计 -->
          <el-card class="stats-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><PieChart /></el-icon>
                <span>访问统计</span>
              </div>
            </template>
            
            <div class="stats-content">
              <div class="stat-item">
                <div class="stat-value">{{ accessStats.totalRequests }}</div>
                <div class="stat-label">总请求数</div>
              </div>
              <div class="stat-item">
                <div class="stat-value success">{{ accessStats.allowedRequests }}</div>
                <div class="stat-label">允许请求</div>
              </div>
              <div class="stat-item">
                <div class="stat-value danger">{{ accessStats.blockedRequests }}</div>
                <div class="stat-label">拒绝请求</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ accessStats.uniqueIPs }}</div>
                <div class="stat-label">唯一IP数</div>
              </div>
            </div>
          </el-card>

          <!-- 最近访问 -->
          <el-card class="recent-access-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Clock /></el-icon>
                <span>最近访问</span>
              </div>
            </template>
            
            <div class="recent-access-list">
              <div v-for="access in recentAccess" :key="access.id" class="access-item">
                <div class="access-info">
                  <el-text class="access-ip">{{ access.ip }}</el-text>
                  <el-tag :type="access.allowed ? 'success' : 'danger'" size="small">
                    {{ access.allowed ? '允许' : '拒绝' }}
                  </el-tag>
                </div>
                <div class="access-time">
                  <el-text type="info" size="small">{{ formatTime(access.timestamp) }}</el-text>
                </div>
              </div>
              
              <div v-if="recentAccess.length === 0" class="no-access">
                <el-text type="info">暂无访问记录</el-text>
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card class="quick-actions-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon><Operation /></el-icon>
                <span>快速操作</span>
              </div>
            </template>
            
            <div class="quick-actions">
              <el-button type="primary" @click="saveWhitelist" :loading="saving" style="width: 100%; margin-bottom: 10px;">
                <el-icon><Check /></el-icon>
                保存配置
              </el-button>
              <el-button @click="loadWhitelist" :loading="loading" style="width: 100%; margin-bottom: 10px;">
                <el-icon><Refresh /></el-icon>
                重载配置
              </el-button>
              <el-button type="warning" @click="clearStats" style="width: 100%; margin-bottom: 10px;">
                <el-icon><Delete /></el-icon>
                清空统计
              </el-button>
              <el-button @click="exportWhitelist" style="width: 100%;">
                <el-icon><Download /></el-icon>
                导出配置
              </el-button>
            </div>
          </el-card>
        </div>
      </el-col>
    </el-row>

    <!-- 添加IP对话框 -->
    <el-dialog
      v-model="showAddDialog"
      title="添加白名单IP"
      width="500px"
    >
      <el-form :model="newEntry" :rules="entryRules" ref="entryFormRef" label-width="120px">
        <el-form-item label="IP地址" prop="ip">
          <el-input v-model="newEntry.ip" placeholder="*************" />
        </el-form-item>
        <el-form-item label="子网掩码" prop="netmask">
          <el-select v-model="newEntry.netmask" style="width: 100%">
            <el-option label="*************** (单个IP)" value="***************" />
            <el-option label="************* (/24)" value="*************" />
            <el-option label="*********** (/16)" value="***********" />
            <el-option label="********* (/8)" value="*********" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="newEntry.description" placeholder="描述这个IP的用途" />
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="newEntry.enabled">立即启用</el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelAdd">取消</el-button>
          <el-button type="primary" @click="confirmAdd">添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Shield, Lock, Plus, Search, PieChart, Clock, Operation,
  Check, Refresh, Delete, Download
} from '@element-plus/icons-vue'
import api from '@/utils/api'

// 数据
const whitelist = ref([])
const showAddDialog = ref(false)
const loading = ref(false)
const saving = ref(false)
const validating = ref(false)

const whitelistConfig = reactive({
  enabled: true
})

const newEntry = reactive({
  ip: '',
  netmask: '***************',
  description: '',
  enabled: true
})

const validationForm = reactive({
  testIP: ''
})

const validationResult = ref(null)

const accessStats = reactive({
  totalRequests: 0,
  allowedRequests: 0,
  blockedRequests: 0,
  uniqueIPs: 0
})

const recentAccess = ref([])

// 验证规则
const entryRules = {
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, message: 'IP地址格式不正确', trigger: 'blur' }
  ],
  netmask: [
    { required: true, message: '请选择子网掩码', trigger: 'change' }
  ]
}

// 方法
const loadWhitelist = async () => {
  loading.value = true
  try {
    const response = await api.get('/api/whitelist')
    whitelist.value = response.data.data || []
    whitelistConfig.enabled = response.data.enabled !== false
    ElMessage.success('白名单配置加载完成')
  } catch (error) {
    ElMessage.error('加载白名单失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const saveWhitelist = async () => {
  saving.value = true
  try {
    await api.post('/api/whitelist/update', {
      enabled: whitelistConfig.enabled,
      entries: whitelist.value
    })
    ElMessage.success('白名单配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

const toggleWhitelist = async () => {
  try {
    await api.post('/api/whitelist/toggle', {
      enabled: whitelistConfig.enabled
    })
    ElMessage.success(`白名单已${whitelistConfig.enabled ? '启用' : '禁用'}`)
  } catch (error) {
    ElMessage.error('状态切换失败: ' + error.message)
  }
}

const confirmAdd = () => {
  // 检查IP是否已存在
  const exists = whitelist.value.some(entry => entry.ip === newEntry.ip)
  if (exists) {
    ElMessage.error('该IP地址已存在')
    return
  }

  whitelist.value.push({ ...newEntry })
  cancelAdd()
  ElMessage.success('IP添加成功')
}

const cancelAdd = () => {
  showAddDialog.value = false
  Object.assign(newEntry, {
    ip: '',
    netmask: '***************',
    description: '',
    enabled: true
  })
}

const deleteEntry = (index) => {
  whitelist.value.splice(index, 1)
  ElMessage.success('删除成功')
}

const toggleEntry = (entry, index) => {
  entry.enabled = !entry.enabled
  ElMessage.success(`条目已${entry.enabled ? '启用' : '禁用'}`)
}

const updateDescription = (entry) => {
  // 描述更新逻辑
}

const validateIP = async () => {
  if (!validationForm.testIP) {
    ElMessage.warning('请输入要测试的IP地址')
    return
  }

  validating.value = true
  try {
    const response = await api.post('/api/whitelist/validate', {
      ip: validationForm.testIP
    })
    validationResult.value = response.data
  } catch (error) {
    ElMessage.error('验证失败: ' + error.message)
  } finally {
    validating.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await api.get('/api/whitelist/stats')
    Object.assign(accessStats, response.data.data)
  } catch (error) {
    console.error('加载统计失败:', error)
  }
}

const loadRecentAccess = async () => {
  try {
    const response = await api.get('/api/whitelist/recent-access')
    recentAccess.value = response.data.data || []
  } catch (error) {
    console.error('加载访问记录失败:', error)
  }
}

const clearStats = async () => {
  try {
    await ElMessageBox.confirm('确定要清空访问统计吗？', '确认清空', { type: 'warning' })
    await api.post('/api/whitelist/clear-stats')
    await loadStats()
    recentAccess.value = []
    ElMessage.success('统计数据已清空')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败: ' + error.message)
    }
  }
}

const exportWhitelist = async () => {
  try {
    const response = await api.get('/api/whitelist/export', { responseType: 'blob' })
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.download = 'whitelist-config.json'
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('白名单配置导出成功')
  } catch (error) {
    ElMessage.error('导出失败: ' + error.message)
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

onMounted(() => {
  loadWhitelist()
  loadStats()
  loadRecentAccess()
  
  // 定期刷新统计和访问记录
  setInterval(() => {
    loadStats()
    loadRecentAccess()
  }, 30000) // 30秒更新一次
})
</script>

<style scoped>
.whitelist-management {
  padding: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #409eff;
}

.page-title .el-icon {
  margin-right: 10px;
}

.whitelist-card, .validation-card, .stats-card, .recent-access-card, .quick-actions-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header span {
  margin-left: 8px;
  font-weight: bold;
}

.whitelist-footer {
  margin-top: 15px;
  text-align: center;
}

.validation-result {
  margin-top: 15px;
}

.stats-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #606266;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.danger {
  color: #f56c6c;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.recent-access-list {
  max-height: 200px;
  overflow-y: auto;
}

.access-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.access-item:last-child {
  border-bottom: none;
}

.access-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.access-ip {
  font-family: monospace;
  font-weight: 500;
}

.no-access {
  text-align: center;
  padding: 20px;
  color: #909399;
}

.quick-actions .el-button {
  margin-bottom: 10px;
}

.quick-actions .el-button:last-child {
  margin-bottom: 0;
}

.ip-address {
  font-family: monospace;
  font-weight: 500;
}
</style>