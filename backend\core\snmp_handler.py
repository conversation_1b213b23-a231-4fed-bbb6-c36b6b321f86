#!/usr/bin/env python3
"""
异步SNMP处理器

处理SNMP协议请求，支持GET、GETNEXT、GETBULK等操作。

作者: SNMP-Modbus Bridge Team V2
版本: 2.2.0
"""

import asyncio
import logging
import bisect
from typing import List, Tuple, Any
from pysnmp.carrier.asyncio.dispatch import AsyncioDispatcher
from pysnmp.carrier.asyncio.dgram import udp, udp6
from pyasn1.codec.ber import encoder, decoder
from pysnmp.proto import api

from .oid_manager import OIDManager
from ..utils.exceptions import SNMPError

logger = logging.getLogger(__name__)

class AsyncSNMPHandler:
    """异步SNMP处理器"""
    
    def __init__(self, oid_manager: OIDManager, listen_ip: str = '0.0.0.0', 
                 listen_port: int = 1161, community: str = 'public'):
        self.oid_manager = oid_manager
        self.listen_ip = listen_ip
        self.listen_port = listen_port
        self.community = community
        
        self.transport_dispatcher = None
        self.is_running = False
        
        logger.info(f"初始化SNMP处理器: {listen_ip}:{listen_port}")
    
    async def start(self):
        """启动SNMP服务"""
        if self.is_running:
            return
        
        logger.info("启动SNMP服务")
        
        # 创建传输调度器
        self.transport_dispatcher = AsyncioDispatcher()
        self.transport_dispatcher.register_recv_callback(self._snmp_callback)
        
        # 注册UDP/IPv4传输
        self.transport_dispatcher.register_transport(
            udp.DOMAIN_NAME,
            udp.UdpAsyncioTransport().open_server_mode((self.listen_ip, self.listen_port))
        )
        
        # 注册UDP/IPv6传输（可选）
        try:
            self.transport_dispatcher.register_transport(
                udp6.DOMAIN_NAME,
                udp6.Udp6AsyncioTransport().open_server_mode(("::", self.listen_port))
            )
            logger.debug("IPv6传输注册成功")
        except Exception as e:
            logger.warning(f"IPv6传输注册失败: {e}")
        
        self.transport_dispatcher.job_started(1)
        self.is_running = True
        
        logger.info(f"SNMP服务已启动: {self.listen_ip}:{self.listen_port}")
    
    async def stop(self):
        """停止SNMP服务"""
        if not self.is_running:
            return
        
        logger.info("停止SNMP服务")
        
        if self.transport_dispatcher:
            self.transport_dispatcher.close_dispatcher()
            self.transport_dispatcher = None
        
        self.is_running = False
        logger.info("SNMP服务已停止")
    
    def _snmp_callback(self, transport_dispatcher, transport_domain, 
                      transport_address, whole_msg):
        """SNMP请求回调函数"""
        logger.debug(f"收到SNMP请求: {transport_address}")
        
        while whole_msg:
            msg_ver = api.decodeMessageVersion(whole_msg)
            if msg_ver in api.PROTOCOL_MODULES:
                p_mod = api.PROTOCOL_MODULES[msg_ver]
            else:
                logger.error(f"不支持的SNMP版本: {msg_ver}")
                return
            
            req_msg, whole_msg = decoder.decode(whole_msg, asn1Spec=p_mod.Message())
            rsp_msg = p_mod.apiMessage.get_response(req_msg)
            rsp_pdu = p_mod.apiMessage.get_pdu(rsp_msg)
            req_pdu = p_mod.apiMessage.get_pdu(req_msg)
            
            # 在新的任务中处理请求，避免阻塞
            asyncio.create_task(self._process_request(
                p_mod, req_pdu, rsp_pdu, rsp_msg, msg_ver, 
                transport_dispatcher, transport_domain, transport_address
            ))
        
        return whole_msg
    
    async def _process_request(self, p_mod, req_pdu, rsp_pdu, rsp_msg, msg_ver,
                             transport_dispatcher, transport_domain, transport_address):
        """处理SNMP请求"""
        try:
            var_binds = []
            pending_errors = []
            error_index = 0
            
            # GET请求
            if req_pdu.isSameTypeWith(p_mod.GetRequestPDU()):
                logger.debug("处理GET请求")
                var_binds = await self._handle_get_request(req_pdu, p_mod, msg_ver)
            
            # GETNEXT请求
            elif req_pdu.isSameTypeWith(p_mod.GetNextRequestPDU()):
                logger.debug("处理GETNEXT请求")
                var_binds, pending_errors = await self._handle_getnext_request(
                    req_pdu, p_mod, msg_ver
                )
            
            # GETBULK请求
            elif req_pdu.isSameTypeWith(p_mod.GetBulkRequestPDU()):
                logger.debug("处理GETBULK请求")
                var_binds, pending_errors = await self._handle_getbulk_request(
                    req_pdu, p_mod, msg_ver
                )
            
            else:
                logger.error("不支持的请求类型")
                p_mod.apiPDU.set_error_status(rsp_pdu, "genErr")
            
            # 设置响应
            p_mod.apiPDU.set_varbinds(rsp_pdu, var_binds)
            
            # 设置错误
            for f, i in pending_errors:
                f(rsp_pdu, i)
            
            # 发送响应
            transport_dispatcher.send_message(
                encoder.encode(rsp_msg), transport_domain, transport_address
            )
            
        except Exception as e:
            logger.error(f"SNMP请求处理异常: {e}")
    
    async def _handle_get_request(self, req_pdu, p_mod, msg_ver) -> List[Tuple]:
        """处理GET请求"""
        var_binds = []
        
        for oid, val in p_mod.apiPDU.get_varbinds(req_pdu):
            try:
                oid_tuple = tuple(oid)
                value = await self.oid_manager.handle_get_request(oid_tuple, msg_ver)
                var_binds.append((oid, value))
                
                logger.debug(f"GET响应: {oid} = {value}")
                
            except Exception as e:
                logger.error(f"GET请求处理异常 {oid}: {e}")
                error_value = api.PROTOCOL_MODULES[msg_ver].Integer(-99998)
                var_binds.append((oid, error_value))
        
        return var_binds
    
    async def _handle_getnext_request(self, req_pdu, p_mod, msg_ver) -> Tuple[List, List]:
        """处理GETNEXT请求"""
        var_binds = []
        pending_errors = []
        error_index = 0
        
        for oid, val in p_mod.apiPDU.get_varbinds(req_pdu):
            error_index += 1
            try:
                oid_tuple = tuple(oid)
                result = await self.oid_manager.handle_getnext_request(
                    oid_tuple, msg_ver
                )
                
                if result is None:
                    # 到达MIB末尾
                    var_binds.append((oid, api.PROTOCOL_MODULES[msg_ver].Null()))
                    pending_errors.append((p_mod.apiPDU.set_end_of_mib_error, error_index))
                else:
                    next_oid, value = result
                    var_binds.append((next_oid, value))
                    logger.debug(f"GETNEXT响应: {next_oid} = {value}")
                
            except Exception as e:
                logger.error(f"GETNEXT请求处理异常 {oid}: {e}")
                var_binds.append((oid, val))
                pending_errors.append((p_mod.apiPDU.set_gen_error, error_index))
        
        return var_binds, pending_errors
    
    async def _handle_getbulk_request(self, req_pdu, p_mod, msg_ver) -> Tuple[List, List]:
        """处理GETBULK请求"""
        var_binds = []
        pending_errors = []
        error_index = 0
        
        try:
            # 获取GETBULK参数
            non_repeaters = int(req_pdu.getComponentByName('non-repeaters'))
            max_repetitions = int(req_pdu.getComponentByName('max-repetitions'))
            
            logger.debug(f"GETBULK参数: non_repeaters={non_repeaters}, "
                        f"max_repetitions={max_repetitions}")
            
            # 获取请求的OID列表
            request_oids = []
            for oid, val in p_mod.apiPDU.get_varbinds(req_pdu):
                request_oids.append(tuple(oid))
            
            # 处理non-repeaters
            for i in range(min(non_repeaters, len(request_oids))):
                error_index += 1
                oid_tuple = request_oids[i]
                
                result = await self.oid_manager.handle_getnext_request(
                    oid_tuple, msg_ver
                )
                
                if result is None:
                    var_binds.append((oid_tuple, api.PROTOCOL_MODULES[msg_ver].Null()))
                    pending_errors.append((p_mod.apiPDU.set_end_of_mib_error, error_index))
                else:
                    next_oid, value = result
                    var_binds.append((next_oid, value))
            
            # 处理repeaters
            repeater_oids = request_oids[non_repeaters:]
            
            for repetition in range(max_repetitions):
                should_continue = False  # 标记是否应该继续下一轮查询
                
                for oid_tuple in repeater_oids:
                    error_index += 1
                    
                    # 计算当前查询的OID
                    current_oid = oid_tuple
                    next_oid = None
                    
                    for _ in range(repetition + 1):
                        result = await self.oid_manager.handle_getnext_request(
                            current_oid, msg_ver
                        )
                        if result is None:
                            # 到达边界，不添加Null值，直接结束
                            next_oid = None
                            break
                        next_oid, _ = result
                        current_oid = next_oid
                    
                    if next_oid is not None:
                        result = await self.oid_manager.handle_getnext_request(
                            current_oid, msg_ver
                        )
                        if result is not None:
                            final_oid, value = result
                            var_binds.append((final_oid, value))
                            should_continue = True  # 有有效结果，可以继续
                        else:
                            # 到达边界，不添加Null值
                            break
                    else:
                        # 到达边界，不添加Null值
                        break
                
                # 如果这一轮没有找到任何有效结果，停止继续查询
                if not should_continue:
                    break
            
        except Exception as e:
            logger.error(f"GETBULK请求处理异常: {e}")
            pending_errors.append((p_mod.apiPDU.set_gen_error, 1))
        
        return var_binds, pending_errors
    
    def get_status(self) -> dict:
        """获取SNMP服务状态"""
        return {
            'is_running': self.is_running,
            'listen_address': f"{self.listen_ip}:{self.listen_port}",
            'community': self.community
        }