# SNMP-Modbus Bridge WebUI 配置界面

## 🎨 界面设计概述

全新的WebUI配置界面提供了友好、现代化的管理体验，支持完整的SNMP-Modbus桥接服务配置和管理功能。

## 🚀 核心功能

### 1. SNMP-Modbus 桥接配置 (`/snmp-config`)

**主要功能：**
- **SNMP基础配置**：监听IP、端口、社区字符串、时区设置
- **Modbus连接配置**：支持TCP和RTU两种模式
  - TCP模式：服务器IP、端口、超时时间、重连间隔
  - RTU模式：串口设备、波特率、数据位、校验位、停止位
- **OID映射管理**：支持系统固定值和Modbus地址映射
  - 系统OID：固定值、系统运行时间、UTC时间
  - Modbus OID：寄存器地址、设备ID、功能码、数据类型
- **实时配置预览**：显示生成的config.ini内容
- **服务控制**：保存配置、重载配置、测试连接、重启服务

**界面特色：**
- 左右分栏布局，配置区域和操作区域分离
- 实时表单验证和状态反馈
- 模式切换时动态显示对应配置选项
- 操作结果即时提示

### 2. Linux 网络接口配置 (`/network`)

**主要功能：**
- **网络接口管理**：
  - 查看所有网络接口状态（UP/DOWN）
  - 支持静态IP和DHCP配置
  - 接口启用/禁用控制
- **网络参数配置**：
  - IP地址、子网掩码设置
  - 默认网关配置
  - DNS服务器设置（主DNS、备用DNS）
- **网络工具**：
  - 网络连通性测试（ping测试）
  - 网络服务重启
  - 网络配置导出

**界面特色：**
- 表格形式展示网络接口信息
- 状态标签直观显示接口状态
- 右侧状态面板显示网络概览
- 配置对话框支持静态/DHCP模式切换

### 3. SNMP 访问白名单管理 (`/whitelist`)

**主要功能：**
- **白名单配置**：
  - 支持最多10个IP/网段条目
  - IP地址 + 子网掩码配置
  - 单个条目启用/禁用控制
  - 描述信息管理
- **访问控制**：
  - 全局白名单启用/禁用开关
  - IP访问验证工具
  - 实时访问统计和监控
- **访问分析**：
  - 总请求数、允许/拒绝请求统计
  - 最近访问记录显示
  - 唯一IP统计

**界面特色：**
- 表格编辑模式，支持内联描述编辑
- 访问统计卡片显示关键指标
- 最近访问时间线展示
- IP验证工具提供即时反馈

## 🛠️ 技术实现

### 前端技术栈
- **Vue 3** + **Composition API**：现代化响应式框架
- **Element Plus**：专业的UI组件库
- **Vue Router 4**：路由管理
- **Pinia**：状态管理
- **Axios**：HTTP请求库

### 组件架构
```
src/views/
├── SNMPModbusConfig.vue         # SNMP-Modbus配置页面
├── EnhancedNetworkConfig.vue    # 增强的网络配置页面
├── EnhancedWhitelistManagement.vue # 增强的白名单管理页面
├── ConfigManagement.vue         # 原配置文件管理
├── SystemStatus.vue             # 系统状态监控
├── LogViewer.vue               # 日志查看器
└── BackupManagement.vue        # 备份管理
```

### API接口规范
```javascript
// SNMP-Modbus配置
GET    /api/config                    # 获取配置
POST   /api/config/update            # 更新配置
POST   /api/bridge/control           # 服务控制

// 网络配置
GET    /api/network/interfaces       # 获取网络接口
POST   /api/network/configure        # 配置接口
POST   /api/network/gateway          # 设置网关
POST   /api/network/dns              # 设置DNS

// 白名单管理
GET    /api/whitelist                # 获取白名单
POST   /api/whitelist/update         # 更新白名单
POST   /api/whitelist/validate       # 验证IP访问
GET    /api/whitelist/stats          # 获取访问统计
```

## 🎯 用户体验设计

### 1. 友好的设置界面
- **现代化设计**：采用卡片式布局，清晰的视觉层次
- **响应式设计**：适配不同屏幕尺寸
- **状态反馈**：操作结果即时显示，加载状态明确指示
- **表单验证**：实时输入验证，错误提示友好

### 2. 操作便捷性
- **一键操作**：常用功能提供快捷按钮
- **批量管理**：支持批量配置和管理
- **配置预览**：实时显示配置效果
- **撤销机制**：重要操作提供确认对话框

### 3. 信息可视化
- **状态图表**：访问统计以图表形式展示
- **实时监控**：网络状态和服务状态实时更新
- **历史记录**：访问记录和操作日志追踪

## 🔧 部署和使用

### 开发环境启动
```bash
# 安装依赖
cd frontend
npm install

# 启动开发服务器
npm run dev

# 后端服务
cd ../backend
python main.py
```

### 生产环境部署
```bash
# 构建前端
cd frontend
npm run build

# 启动生产服务
cd ../
./start-prod.sh  # Linux
start-prod.bat   # Windows
```

### 访问地址
- **开发环境**：http://localhost:3000
- **生产环境**：http://localhost:8000

### 默认登录
- **用户名**：admin
- **密码**：admin1234

## 📋 配置文件格式

生成的 `config.ini` 文件格式：

```ini
[SNMP_BRIDGE_CONFIG]
listen_ip = 0.0.0.0
listen_port = 1161
community = public
modbus_type = TCP
timezone_offset = +08

[MODBUS_TCP_CONFIG]
server_ip = 127.0.0.1
port = 502
timeout = 3
retry_interval = 10

[SYSTEM_OID_1]
oid = .*******.*******.0
description = System Description
type = fixed_value
value = SNMP-Modbus Bridge v2.0
snmp_data_type = OctetString

[SNMP_OID_1]
oid = .*******.4.1.41475.********.1.2.0
description = Temperature Sensor
register_address = 100
unit_id = 1
function_code = 3
data_type = uint16
snmp_data_type = Integer
```

## 🔒 安全特性

- **用户认证**：JWT令牌认证机制
- **白名单控制**：IP访问白名单保护
- **操作日志**：关键操作记录追踪
- **权限控制**：基于角色的访问控制
- **HTTPS支持**：支持SSL/TLS加密传输

## 🎨 界面截图

（在实际部署后添加界面截图）

## 📞 技术支持

如有问题，请联系技术支持团队或查看项目文档：
- **项目仓库**：SNMP-Modbus Bridge v2
- **文档**：README.md, INSTALL.md, TROUBLESHOOTING.md
- **日志**：查看 `/logs/` 目录下的日志文件

---

**SNMP-Modbus Bridge Team V2**  
*现代化的工业协议桥接解决方案*