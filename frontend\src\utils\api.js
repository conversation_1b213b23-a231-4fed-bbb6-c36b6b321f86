import axios from 'axios'
import { ElMessage, ElLoading } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 令牌管理
let authToken = localStorage.getItem('auth_token') || null

// 设置令牌
export const setAuthToken = (token) => {
  authToken = token
  if (token) {
    localStorage.setItem('auth_token', token)
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`
  } else {
    localStorage.removeItem('auth_token')
    delete api.defaults.headers.common['Authorization']
  }
}

// 获取令牌
export const getAuthToken = () => authToken

// 清除令牌
export const clearAuthToken = () => setAuthToken(null)

// 初始化时设置令牌
if (authToken) {
  setAuthToken(authToken)
}

// 请求拦截器
let loadingInstance = null

api.interceptors.request.use(
  (config) => {
    // 显示加载动画
    if (config.showLoading !== false) {
      loadingInstance = ElLoading.service({
        lock: true,
        text: '处理中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    }
    return config
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    
    const { data } = response
    
    // 检查业务状态码
    if (data.success === false) {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    return data
  },
  (error) => {
    if (loadingInstance) {
      loadingInstance.close()
    }
    
    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      switch (status) {
        case 400:
          message = data.detail || '请求参数错误'
          break
        case 401:
          message = '未授权访问或令牌已过期'
          // 自动清除令牌并跳转到登录页
          clearAuthToken()
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          message = '禁止访问'
          break
        case 404:
          message = data.detail || '资源不存在'
          break
        case 500:
          message = data.detail || '服务器内部错误'
          break
        default:
          message = data.detail || `请求失败 (${status})`
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络设置'
    }
    
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// API方法定义
export const configApi = {
  // 获取完整配置
  getConfig: () => api.get('/config'),
  
  // 获取指定配置节
  getConfigSection: (section) => api.get(`/config/${section}`),
  
  // 更新配置项
  updateConfigItem: (section, key, value) => 
    api.put(`/config/${section}/${key}`, { value }),
  
  // 创建配置节
  createConfigSection: (sectionData) => 
    api.post('/config/section', sectionData),
  
  // 删除配置节
  deleteConfigSection: (section) => 
    api.delete(`/config/${section}`),
  
  // 删除配置项
  deleteConfigItem: (section, key) => 
    api.delete(`/config/${section}/${key}`),
  
  // 获取备份列表
  getBackups: () => api.get('/config/backups'),
  
  // 手动备份
  createBackup: () => api.post('/config/backup'),
  
  // 恢复配置
  restoreConfig: (backupFilename) => 
    api.post(`/config/restore/${backupFilename}`),
  
  // 下载配置文件
  downloadConfig: () => {
    return axios.get('/api/config/download', {
      responseType: 'blob',
    })
  },
  
  // 上传配置文件
  uploadConfig: (file) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/config/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },
}

export const networkApi = {
  // 获取网络接口列表
  getInterfaces: () => api.get('/network/interfaces'),
  
  // 获取指定接口信息
  getInterface: (interfaceName) => 
    api.get(`/network/interfaces/${interfaceName}`),
  
  // 配置网络接口
  configureInterface: (interfaceName, config) => 
    api.put(`/network/interfaces/${interfaceName}`, config),
}

export const systemApi = {
  // 健康检查
  healthCheck: () => api.get('/health', { showLoading: false }),
}

// 认证API
export const authApi = {
  // 用户登录
  login: (credentials) => api.post('/auth/login', credentials),
  
  // 用户登出
  logout: () => api.post('/auth/logout'),
  
  // 获取当前用户信息
  getUserInfo: () => api.get('/auth/user'),
}

// NMS白名单API
export const whitelistApi = {
  // 获取白名单配置
  getWhitelist: () => api.get('/whitelist'),
  
  // 更新白名单条目
  updateEntry: (entryId, entry) => api.put(`/whitelist/${entryId}`, entry),
  
  // 更新白名单全局配置
  updateConfig: (config) => api.put('/whitelist/config', config),
}

// 日志管理API
export const logApi = {
  // 获取日志文件列表
  getLogFiles: () => api.get('/logs'),
  
  // 读取日志文件内容
  readLog: (fileName, params = {}) => {
    const queryParams = new URLSearchParams()
    if (params.lines) queryParams.append('lines', params.lines)
    if (params.search) queryParams.append('search', params.search)
    
    const queryString = queryParams.toString()
    const url = `/logs/${fileName}${queryString ? '?' + queryString : ''}`
    return api.get(url)
  },
  
  // 下载日志文件
  downloadLog: (fileName) => {
    return axios.get(`/api/logs/download/${fileName}`, {
      responseType: 'blob',
      headers: {
        'Authorization': api.defaults.headers.common['Authorization']
      }
    })
  },
}

export default api