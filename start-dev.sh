#!/bin/bash

# Web配置管理工具开发环境启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查开发环境依赖..."
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 安装后端依赖
setup_backend() {
    log_info "设置后端环境..."
    
    cd backend
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        python3 -m venv venv
        log_success "创建虚拟环境"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    pip install --upgrade pip
    pip install -r requirements.txt
    
    log_success "后端环境设置完成"
    
    cd ..
}

# 安装前端依赖
setup_frontend() {
    log_info "设置前端环境..."
    
    cd frontend
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        npm install
        log_success "前端依赖安装完成"
    else
        log_info "前端依赖已安装"
    fi
    
    cd ..
}

# 启动开发服务器
start_dev_servers() {
    log_info "启动开发服务器..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动后端服务器
    log_info "启动后端服务器 (端口 8000)..."
    cd backend
    source venv/bin/activate
    nohup python main.py > ../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../logs/backend.pid
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 检查后端是否启动成功
    if kill -0 $BACKEND_PID 2>/dev/null; then
        log_success "后端服务器启动成功 (PID: $BACKEND_PID)"
    else
        log_error "后端服务器启动失败"
        exit 1
    fi
    
    # 启动前端开发服务器
    log_info "启动前端开发服务器 (端口 3000)..."
    cd frontend
    nohup npm run dev > ../logs/frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > ../logs/frontend.pid
    cd ..
    
    # 等待前端启动
    sleep 5
    
    # 检查前端是否启动成功
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        log_success "前端开发服务器启动成功 (PID: $FRONTEND_PID)"
    else
        log_error "前端开发服务器启动失败"
        # 停止后端服务器
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
}

# 显示访问信息
show_dev_info() {
    log_success "开发环境启动完成！"
    echo ""
    echo "=========================================="
    echo "  开发环境访问信息"
    echo "=========================================="
    echo ""
    echo "前端开发服务器:"
    echo "  http://localhost:3000"
    echo ""
    echo "后端API服务器:"
    echo "  http://localhost:8000"
    echo ""
    echo "API文档:"
    echo "  http://localhost:8000/docs"
    echo ""
    echo "日志文件:"
    echo "  后端日志: logs/backend.log"
    echo "  前端日志: logs/frontend.log"
    echo ""
    echo "停止服务:"
    echo "  ./stop-dev.sh"
    echo ""
    echo "=========================================="
    echo ""
    log_info "按 Ctrl+C 停止所有服务"
}

# 等待中断信号
wait_for_interrupt() {
    # 创建停止脚本
    cat > stop-dev.sh << 'EOF'
#!/bin/bash

echo "停止开发服务器..."

# 停止后端服务器
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "后端服务器已停止"
    fi
    rm -f logs/backend.pid
fi

# 停止前端服务器
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "前端服务器已停止"
    fi
    rm -f logs/frontend.pid
fi

echo "所有开发服务器已停止"
EOF
    chmod +x stop-dev.sh
    
    # 等待中断信号
    trap 'log_info "收到中断信号，正在停止服务..."; ./stop-dev.sh; exit 0' INT TERM
    
    while true; do
        sleep 1
    done
}

# 主函数
main() {
    echo "=========================================="
    echo "  SNMP-Modbus Bridge Web配置管理工具"
    echo "  开发环境启动脚本 v1.0.0"
    echo "=========================================="
    echo ""
    
    check_dependencies
    setup_backend
    setup_frontend
    start_dev_servers
    show_dev_info
    wait_for_interrupt
}

# 运行主函数
main "$@"