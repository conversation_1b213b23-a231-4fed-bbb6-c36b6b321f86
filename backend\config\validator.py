"""
配置验证器

提供配置文件的格式和内容验证功能。

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import re
import ipaddress
import logging
from typing import List, Dict, Any
from utils.exceptions import ConfigurationError

logger = logging.getLogger(__name__)

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def validate_snmp_config(self, snmp_config) -> List[str]:
        """验证SNMP配置"""
        errors = []
        
        # 验证监听IP
        try:
            ipaddress.ip_address(snmp_config.listen_ip)
        except ValueError:
            if snmp_config.listen_ip != '0.0.0.0':
                errors.append(f"无效的SNMP监听IP: {snmp_config.listen_ip}")
        
        # 验证端口范围
        if not (1 <= snmp_config.listen_port <= 65535):
            errors.append(f"SNMP端口超出范围: {snmp_config.listen_port}")
        
        if not snmp_config.community:
            errors.append("SNMP社区字符串不能为空")
        
        return errors
    
    def validate_modbus_config(self, modbus_config) -> List[str]:
        """验证Modbus配置"""
        errors = []
        
        if modbus_config.connection_type == 'TCP':
            try:
                ipaddress.ip_address(modbus_config.server_ip)
            except ValueError:
                errors.append(f"无效的Modbus服务器IP: {modbus_config.server_ip}")
            
            if not (1 <= modbus_config.port <= 65535):
                errors.append(f"Modbus端口超出范围: {modbus_config.port}")
        
        elif modbus_config.connection_type == 'RTU':
            if not modbus_config.device:
                errors.append("Modbus RTU设备路径不能为空")
        
        if modbus_config.timeout <= 0:
            errors.append("Modbus超时时间必须大于0")
        
        return errors
    
    def validate_oid_format(self, oid: str) -> bool:
        """验证OID格式"""
        if not oid:
            return False
        pattern = r'^\.(\d+\.)*\d+$'
        return bool(re.match(pattern, oid))
    
    def validate_all(self, config_manager) -> Dict[str, List[str]]:
        """验证所有配置"""
        results = {'errors': [], 'warnings': []}
        
        if config_manager.snmp_config:
            errors = self.validate_snmp_config(config_manager.snmp_config)
            results['errors'].extend(errors)
        
        if config_manager.modbus_config:
            errors = self.validate_modbus_config(config_manager.modbus_config)
            results['errors'].extend(errors)
        
        # 验证OID重复
        oids = [m.oid for m in config_manager.oid_mappings]
        duplicates = set([oid for oid in oids if oids.count(oid) > 1])
        for oid in duplicates:
            results['errors'].append(f"重复的OID: {oid}")
        
        if results['errors']:
            logger.error(f"配置验证失败: {len(results['errors'])} 个错误")
        
        return results