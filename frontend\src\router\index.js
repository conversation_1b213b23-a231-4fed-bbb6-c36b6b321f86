import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: {
        title: '用户登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      redirect: '/snmp-config'
    },
    {
      path: '/config',
      name: 'Config',
      component: () => import('@/views/ConfigManagement.vue'),
      meta: {
        title: '配置文件管理',
        requiresAuth: true
      }
    },
    {
      path: '/snmp-config',
      name: 'SNMPConfig',
      component: () => import('@/views/SNMPModbusConfig.vue'),
      meta: {
        title: 'SNMP-Modbus配置',
        requiresAuth: true
      }
    },
    {
      path: '/network',
      name: 'Network',
      component: () => import('@/views/EnhancedNetworkConfig.vue'),
      meta: {
        title: '网络接口配置',
        requiresAuth: true
      }
    },
    {
      path: '/whitelist',
      name: 'Whitelist',
      component: () => import('@/views/EnhancedWhitelistManagement.vue'),
      meta: {
        title: 'NMS白名单管理',
        requiresAuth: true
      }
    },
    {
      path: '/logs',
      name: 'Logs',
      component: () => import('@/views/LogViewer.vue'),
      meta: {
        title: '日志查看器',
        requiresAuth: true
      }
    },
    {
      path: '/backup',
      name: 'Backup',
      component: () => import('@/views/BackupManagement.vue'),
      meta: {
        title: '备份管理',
        requiresAuth: true
      }
    },
    {
      path: '/status',
      name: 'Status',
      component: () => import('@/views/SystemStatus.vue'),
      meta: {
        title: '系统状态',
        requiresAuth: true
      }
    }
  ]
})

router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - SNMP-Modbus Bridge 配置管理`
  }
  
  // 检查是否需要认证
  if (to.meta.requiresAuth === false) {
    // 不需要认证的页面（如登录页）
    next()
    return
  }
  
  // 需要认证的页面
  const authStore = useAuthStore()
  
  try {
    // 检查认证状态
    if (!authStore.isAuthenticated) {
      // 如果有token，尝试验证
      if (authStore.token) {
        await authStore.checkAuthStatus()
      }
      
      // 仍然未认证，跳转到登录页
      if (!authStore.isAuthenticated) {
        ElMessage.warning('请先登录')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
    
    // 认证通过，继续导航
    next()
  } catch (error) {
    console.error('路由守卫错误:', error)
    // 认证失败，跳转到登录页
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  }
})

export default router