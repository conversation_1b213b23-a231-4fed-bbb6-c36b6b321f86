#!/bin/bash

# SNMP-Modbus Bridge Web配置管理工具 - 生产环境启动脚本

echo "🚀 启动SNMP-Modbus Bridge Web配置管理工具（生产环境）"
echo "=================================================="

# 检查Python环境
echo "📋 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装"
    exit 1
fi

python3 --version

# 检查前端构建文件
echo "📋 检查前端构建文件..."
if [ ! -d "frontend/dist" ]; then
    echo "❌ 前端构建文件不存在，请先运行构建脚本:"
    echo "   ./build.sh"
    exit 1
fi

if [ ! -f "frontend/dist/index.html" ]; then
    echo "❌ 前端入口文件不存在"
    exit 1
fi

echo "✅ 前端构建文件检查通过"

# 检查后端依赖
echo "📦 检查后端依赖..."
cd backend
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
    echo "✅ 后端依赖检查完成"
else
    echo "⚠️  requirements.txt 文件不存在，跳过依赖安装"
fi

# 启动生产环境服务
echo "🔧 启动生产环境服务..."
echo "访问地址: http://localhost:8000"
echo "API文档: http://localhost:8000/docs"
echo "默认登录用户名: admin"
echo "默认登录密码: admin1234"
echo "=================================================="
echo "按 Ctrl+C 停止服务..."
echo ""

python3 main.py