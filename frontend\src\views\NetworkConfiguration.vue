<template>
  <div class="network-configuration">
    <div class="page-title">
      <el-icon><Connection /></el-icon>
      网络接口配置
    </div>

    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" class="btn-primary" @click="refreshInterfaces">
          <el-icon><Refresh /></el-icon>
          刷新接口
        </el-button>
        <el-button type="success" @click="showAddInterfaceDialog = true">
          <el-icon><Plus /></el-icon>
          添加接口配置
        </el-button>
      </div>
      <div class="toolbar-right">
        <div class="status-summary">
          <el-tag type="success" size="small">
            活跃: {{ activeInterfaces.length }}
          </el-tag>
          <el-tag type="danger" size="small">
            停用: {{ downInterfaces.length }}
          </el-tag>
          <el-tag type="info" size="small">
            总计: {{ networkStore.interfaces.length }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 网络接口列表 -->
    <div class="interfaces-container" v-loading="networkStore.loading">
      <el-empty v-if="networkStore.interfaces.length === 0" description="暂无网络接口数据">
        <el-button type="primary" @click="refreshInterfaces">重新加载</el-button>
      </el-empty>

      <div v-else class="interfaces-grid">
        <el-card 
          v-for="interface in networkStore.interfaces" 
          :key="interface.name" 
          class="interface-card"
          :class="{ 'interface-up': interface.status === 'UP', 'interface-down': interface.status === 'DOWN' }"
        >
          <template #header>
            <div class="interface-header">
              <div class="interface-info">
                <h3>{{ interface.name }}</h3>
                <div class="status-indicator" :class="interface.status.toLowerCase()">
                  <el-icon v-if="interface.status === 'UP'"><CircleCheckFilled /></el-icon>
                  <el-icon v-else><CircleCloseFilled /></el-icon>
                  {{ interface.status }}
                </div>
              </div>
              <div class="interface-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="configureInterface(interface)"
                >
                  <el-icon><Setting /></el-icon>
                  配置
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="showInterfaceDetails(interface)"
                >
                  <el-icon><View /></el-icon>
                  详情
                </el-button>
              </div>
            </div>
          </template>

          <div class="interface-content">
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="IP地址">
                <el-text v-if="interface.ip" class="ip-address">{{ interface.ip }}</el-text>
                <el-text v-else type="info">未设置</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="子网掩码">
                <el-text v-if="interface.netmask">{{ interface.netmask }}</el-text>
                <el-text v-else type="info">未设置</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="网关">
                <el-text v-if="interface.gateway">{{ interface.gateway }}</el-text>
                <el-text v-else type="info">未设置</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="DNS服务器">
                <div v-if="interface.dns && interface.dns.length > 0">
                  <el-tag v-for="dns in interface.dns" :key="dns" size="small" class="dns-tag">
                    {{ dns }}
                  </el-tag>
                </div>
                <el-text v-else type="info">未设置</el-text>
              </el-descriptions-item>
              <el-descriptions-item label="DHCP">
                <el-tag :type="interface.dhcp ? 'success' : 'info'" size="small">
                  {{ interface.dhcp ? '启用' : '禁用' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 接口配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      :title="`配置网络接口 - ${selectedInterface?.name}`"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="interfaceConfig"
        :rules="configRules"
        ref="configFormRef"
        label-width="120px"
      >
        <el-form-item label="配置方式">
          <el-radio-group v-model="interfaceConfig.dhcp">
            <el-radio :label="true">DHCP 自动获取</el-radio>
            <el-radio :label="false">静态IP配置</el-radio>
          </el-radio-group>
        </el-form-item>

        <template v-if="!interfaceConfig.dhcp">
          <el-form-item label="IP地址" prop="ip">
            <el-input
              v-model="interfaceConfig.ip"
              placeholder="请输入IP地址，如: ***********00"
            />
          </el-form-item>
          <el-form-item label="子网掩码" prop="netmask">
            <el-select v-model="interfaceConfig.netmask" placeholder="选择子网掩码">
              <el-option label="************* (/24)" value="*************" />
              <el-option label="*********** (/16)" value="***********" />
              <el-option label="********* (/8)" value="*********" />
            </el-select>
          </el-form-item>
          <el-form-item label="网关" prop="gateway">
            <el-input
              v-model="interfaceConfig.gateway"
              placeholder="请输入网关地址，如: ***********"
            />
          </el-form-item>
          <el-form-item label="DNS服务器">
            <div class="dns-config">
              <div 
                v-for="(dns, index) in interfaceConfig.dns" 
                :key="index"
                class="dns-item"
              >
                <el-input
                  v-model="interfaceConfig.dns[index]"
                  placeholder="请输入DNS服务器地址"
                  size="small"
                />
                <el-button
                  type="danger"
                  size="small"
                  text
                  @click="removeDnsServer(index)"
                  v-if="interfaceConfig.dns.length > 1"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
              <el-button
                type="primary"
                text
                @click="addDnsServer"
                class="add-dns-btn"
              >
                <el-icon><Plus /></el-icon>
                添加DNS服务器
              </el-button>
            </div>
          </el-form-item>
        </template>
      </el-form>
      
      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveInterfaceConfig" :loading="saving">
          {{ saving ? '配置中...' : '保存配置' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 接口详情对话框 -->
    <el-dialog
      v-model="showDetailsDialog"
      :title="`接口详情 - ${selectedInterface?.name}`"
      width="500px"
    >
      <el-descriptions :column="1" border v-if="selectedInterface">
        <el-descriptions-item label="接口名称">{{ selectedInterface.name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <div class="status-indicator" :class="selectedInterface.status.toLowerCase()">
            <el-icon v-if="selectedInterface.status === 'UP'"><CircleCheckFilled /></el-icon>
            <el-icon v-else><CircleCloseFilled /></el-icon>
            {{ selectedInterface.status }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">
          {{ selectedInterface.ip || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="子网掩码">
          {{ selectedInterface.netmask || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="网关">
          {{ selectedInterface.gateway || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="DNS服务器">
          <div v-if="selectedInterface.dns && selectedInterface.dns.length > 0">
            <div v-for="dns in selectedInterface.dns" :key="dns" class="dns-item">
              {{ dns }}
            </div>
          </div>
          <span v-else>未设置</span>
        </el-descriptions-item>
        <el-descriptions-item label="DHCP状态">
          <el-tag :type="selectedInterface.dhcp ? 'success' : 'info'" size="small">
            {{ selectedInterface.dhcp ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 添加接口配置对话框 -->
    <el-dialog
      v-model="showAddInterfaceDialog"
      title="添加接口配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-alert
        title="注意"
        type="info"
        description="此功能用于为已存在的网络接口添加配置，请确保接口名称正确。"
        show-icon
        :closable="false"
        style="margin-bottom: 20px;"
      />
      <el-form
        :model="newInterfaceConfig"
        :rules="newConfigRules"
        ref="newConfigFormRef"
        label-width="120px"
      >
        <el-form-item label="接口名称" prop="interface">
          <el-input
            v-model="newInterfaceConfig.interface"
            placeholder="请输入接口名称，如: eth0, enp0s3"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddInterfaceDialog = false">取消</el-button>
        <el-button type="primary" @click="addInterfaceConfig">添加配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useNetworkStore } from '@/stores'

const networkStore = useNetworkStore()

// 响应式数据
const showConfigDialog = ref(false)
const showDetailsDialog = ref(false)
const showAddInterfaceDialog = ref(false)
const selectedInterface = ref(null)
const saving = ref(false)
const configFormRef = ref()
const newConfigFormRef = ref()

const interfaceConfig = ref({
  interface: '',
  ip: '',
  netmask: '*************',
  gateway: '',
  dns: ['*******'],
  dhcp: false
})

const newInterfaceConfig = ref({
  interface: ''
})

// 表单验证规则
const configRules = {
  ip: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { 
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, 
      message: '请输入有效的IP地址', 
      trigger: 'blur' 
    }
  ],
  netmask: [
    { required: true, message: '请选择子网掩码', trigger: 'change' }
  ],
  gateway: [
    { 
      pattern: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, 
      message: '请输入有效的网关地址', 
      trigger: 'blur' 
    }
  ]
}

const newConfigRules = {
  interface: [
    { required: true, message: '请输入接口名称', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z][a-zA-Z0-9]*$/, 
      message: '接口名称必须以字母开头，只能包含字母和数字', 
      trigger: 'blur' 
    }
  ]
}

// 计算属性
const activeInterfaces = computed(() => networkStore.activeInterfaces)
const downInterfaces = computed(() => networkStore.downInterfaces)

// 方法
const refreshInterfaces = async () => {
  try {
    await networkStore.loadInterfaces()
    ElMessage.success('网络接口刷新成功')
  } catch (error) {
    ElMessage.error('网络接口刷新失败')
  }
}

const configureInterface = (iface) => {
  selectedInterface.value = iface
  interfaceConfig.value = {
    interface: iface.name,
    ip: iface.ip || '',
    netmask: iface.netmask || '*************',
    gateway: iface.gateway || '',
    dns: iface.dns && iface.dns.length > 0 ? [...iface.dns] : ['*******'],
    dhcp: iface.dhcp || false
  }
  showConfigDialog.value = true
}

const saveInterfaceConfig = async () => {
  try {
    if (!interfaceConfig.value.dhcp) {
      await configFormRef.value.validate()
    }
    
    saving.value = true
    
    const config = {
      ...interfaceConfig.value
    }
    
    // 过滤空的DNS服务器
    config.dns = config.dns.filter(dns => dns.trim() !== '')
    
    await networkStore.configureInterface(selectedInterface.value.name, config)
    ElMessage.success(`网络接口 ${selectedInterface.value.name} 配置成功`)
    showConfigDialog.value = false
    
    // 刷新接口列表
    await refreshInterfaces()
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    saving.value = false
  }
}

const showInterfaceDetails = (iface) => {
  selectedInterface.value = iface
  showDetailsDialog.value = true
}

const addDnsServer = () => {
  interfaceConfig.value.dns.push('')
}

const removeDnsServer = (index) => {
  if (interfaceConfig.value.dns.length > 1) {
    interfaceConfig.value.dns.splice(index, 1)
  }
}

const addInterfaceConfig = async () => {
  try {
    await newConfigFormRef.value.validate()
    
    // 这里可以添加检查接口是否存在的逻辑
    const interfaceName = newInterfaceConfig.value.interface
    
    // 创建一个临时接口对象用于配置
    const tempInterface = {
      name: interfaceName,
      ip: '',
      netmask: '',
      gateway: '',
      dns: [],
      dhcp: false,
      status: 'UNKNOWN'
    }
    
    configureInterface(tempInterface)
    showAddInterfaceDialog.value = false
    newInterfaceConfig.value.interface = ''
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

// 生命周期
onMounted(() => {
  refreshInterfaces()
})
</script>

<style scoped>
.network-configuration {
  padding: 0;
}

.interfaces-container {
  min-height: 400px;
}

.interfaces-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.interface-card {
  transition: all 0.3s ease;
}

.interface-card.interface-up {
  border-left: 4px solid #67c23a;
}

.interface-card.interface-down {
  border-left: 4px solid #f56c6c;
}

.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.interface-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.interface-actions {
  display: flex;
  gap: 8px;
}

.interface-content {
  padding: 0;
}

.ip-address {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #6366f1;
}

.status-summary {
  display: flex;
  gap: 8px;
  align-items: center;
}

.dns-config {
  width: 100%;
}

.dns-item {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.dns-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.add-dns-btn {
  width: 100%;
  margin-top: 8px;
  border: 2px dashed #d1d5db;
  background: #f9fafb;
}

.add-dns-btn:hover {
  border-color: #6366f1;
  background: #eef2ff;
}

@media (max-width: 768px) {
  .interfaces-grid {
    grid-template-columns: 1fr;
  }
  
  .interface-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .interface-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>