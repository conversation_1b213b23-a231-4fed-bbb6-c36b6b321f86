<template>
  <div class="backup-management">
    <div class="page-title">
      <el-icon><FolderOpened /></el-icon>
      备份管理
    </div>

    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" class="btn-primary" @click="refreshBackups">
          <el-icon><Refresh /></el-icon>
          刷新列表
        </el-button>
        <el-button type="success" @click="createBackup">
          <el-icon><DocumentCopy /></el-icon>
          创建备份
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-text type="info">备份文件数量: {{ configStore.backups.length }}</el-text>
      </div>
    </div>

    <!-- 备份文件列表 -->
    <div class="backups-container" v-loading="loading">
      <el-empty v-if="configStore.backups.length === 0" description="暂无备份文件">
        <el-button type="primary" @click="createBackup">创建第一个备份</el-button>
      </el-empty>

      <el-table v-else :data="configStore.backups" stripe>
        <el-table-column prop="filename" label="文件名" min-width="200">
          <template #default="{ row }">
            <div class="filename-cell">
              <el-icon><Document /></el-icon>
              <span class="filename">{{ row.filename }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="size" label="文件大小" width="120">
          <template #default="{ row }">
            <el-text>{{ formatFileSize(row.size) }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_time" label="创建时间" width="180">
          <template #default="{ row }">
            <el-text>{{ formatTime(row.created_time) }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column prop="modified_time" label="修改时间" width="180">
          <template #default="{ row }">
            <el-text>{{ formatTime(row.modified_time) }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="restoreBackup(row)"
              >
                <el-icon><RefreshLeft /></el-icon>
                恢复
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="downloadBackup(row)"
              >
                <el-icon><Download /></el-icon>
                下载
              </el-button>
              <el-popconfirm
                title="确定要删除这个备份文件吗？"
                @confirm="deleteBackup(row)"
              >
                <template #reference>
                  <el-button type="danger" size="small">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </template>
              </el-popconfirm>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 恢复确认对话框 -->
    <el-dialog
      v-model="showRestoreDialog"
      title="确认恢复备份"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="restore-warning">
        <el-alert
          title="警告"
          type="warning"
          description="恢复备份将覆盖当前配置文件，当前配置将自动备份。此操作不可撤销，请确认是否继续。"
          show-icon
          :closable="false"
        />
        
        <div class="backup-info" v-if="selectedBackup">
          <h4>备份文件信息：</h4>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="文件名">{{ selectedBackup.filename }}</el-descriptions-item>
            <el-descriptions-item label="文件大小">{{ formatFileSize(selectedBackup.size) }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatTime(selectedBackup.created_time) }}</el-descriptions-item>
            <el-descriptions-item label="修改时间">{{ formatTime(selectedBackup.modified_time) }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showRestoreDialog = false">取消</el-button>
        <el-button type="danger" @click="confirmRestore" :loading="restoring">
          {{ restoring ? '恢复中...' : '确认恢复' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useConfigStore } from '@/stores'

const configStore = useConfigStore()

// 响应式数据
const loading = ref(false)
const showRestoreDialog = ref(false)
const selectedBackup = ref(null)
const restoring = ref(false)

// 方法
const refreshBackups = async () => {
  try {
    loading.value = true
    await configStore.loadBackups()
    ElMessage.success('备份列表刷新成功')
  } catch (error) {
    ElMessage.error('备份列表刷新失败')
  } finally {
    loading.value = false
  }
}

const createBackup = async () => {
  try {
    await configStore.createBackup()
    ElMessage.success('配置备份创建成功')
  } catch (error) {
    ElMessage.error('配置备份创建失败')
  }
}

const restoreBackup = (backup) => {
  selectedBackup.value = backup
  showRestoreDialog.value = true
}

const confirmRestore = async () => {
  try {
    restoring.value = true
    await configStore.restoreConfig(selectedBackup.value.filename)
    ElMessage.success(`配置已从 ${selectedBackup.value.filename} 恢复`)
    showRestoreDialog.value = false
  } catch (error) {
    ElMessage.error('配置恢复失败')
  } finally {
    restoring.value = false
  }
}

const downloadBackup = async (backup) => {
  try {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = `/api/config/backup/${backup.filename}`
    link.download = backup.filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('备份文件下载开始')
  } catch (error) {
    ElMessage.error('备份文件下载失败')
  }
}

const deleteBackup = async (backup) => {
  try {
    // 这里需要在后端API中添加删除备份的接口
    ElMessage.info('删除备份功能将在后续版本中实现')
  } catch (error) {
    ElMessage.error('删除备份失败')
  }
}

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timeString) => {
  return new Date(timeString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  refreshBackups()
})
</script>

<style scoped>
.backup-management {
  padding: 0;
}

.backups-container {
  min-height: 400px;
}

.filename-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filename {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.restore-warning {
  margin-bottom: 20px;
}

.backup-info {
  margin-top: 20px;
}

.backup-info h4 {
  margin-bottom: 12px;
  color: #2c3e50;
}

@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .action-buttons .el-button {
    width: 100%;
    justify-content: center;
  }
}
</style>