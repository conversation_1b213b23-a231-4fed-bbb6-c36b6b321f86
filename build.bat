@echo off
chcp 65001 >nul
title SNMP-Modbus Bridge Web配置管理工具 - 生产环境构建

echo 🏗️ 开始构建生产环境...
echo ==================================================

rem 检查Node.js
echo 📋 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH环境变量
    echo 请安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm未安装
    pause
    exit /b 1
)

echo ✅ Node.js环境检查通过

rem 进入前端目录
echo 📂 进入前端目录...
cd frontend
if %errorlevel% neq 0 (
    echo ❌ 找不到frontend目录
    pause
    exit /b 1
)

rem 安装依赖
echo 📦 安装前端依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)

echo ✅ 前端依赖安装完成

rem 清理旧的构建文件
if exist dist (
    echo 🧹 清理旧的构建文件...
    rd /s /q dist
)

rem 构建生产环境
echo 🏗️ 开始构建前端生产环境...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)

rem 检查构建结果
if not exist dist (
    echo ❌ 前端构建失败，dist目录不存在
    pause
    exit /b 1
)

if not exist dist\index.html (
    echo ❌ 前端构建失败，index.html文件不存在
    pause
    exit /b 1
)

echo ✅ 前端构建完成！
echo.

rem 显示构建结果
echo ==========================================
echo   构建结果
echo ==========================================
echo.
echo 构建目录: %cd%\dist
echo 入口文件: %cd%\dist\index.html
echo.

echo 文件列表:
dir dist /b

echo.
echo ==========================================
echo   部署说明
echo ==========================================
echo.
echo 1. 生产环境运行方式:
echo    cd ..\backend
echo    python main.py
echo.
echo 2. 访问地址:
echo    http://localhost:8000
echo.
echo 3. 默认登录信息:
echo    用户名: admin
echo    密码: admin1234
echo.
echo ==========================================

cd ..
echo ✅ 构建脚本执行完成！
pause