#!/usr/bin/env python3
"""
配置文件加载器 - Utils模块版本

从 config.ini 文件加载配置并转换为 Python 对象格式，
整合到 utils 模块中，提供统一的配置管理功能。

作者: SNMP-Modbus Bridge Team
版本: 2.0
日期: 2025-09-05
"""

import configparser
import os
import logging
from typing import Dict, List, Any

logger = logging.getLogger(__name__)

class ConfigLoader:
    """配置文件加载器"""
    
    def __init__(self, config_file='config.ini'):
        """
        初始化配置加载器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件不存在: {self.config_file}")
        
        self.config.read(self.config_file, encoding='utf-8')
        logger.debug(f"✓ 配置文件已加载: {self.config_file}")
    
    def reload_config(self):
        """重新加载配置文件"""
        logger.info("🔄 重新加载配置文件...")
        self.load_config()
        logger.info("✅ 配置文件重新加载完成")
    
    def get_snmp_bridge_config(self) -> Dict[str, Any]:
        """获取 SNMP 桥接配置"""
        section = 'SNMP_BRIDGE_CONFIG'
        if section not in self.config:
            raise ValueError(f"配置文件中缺少 [{section}] 部分")
        
        raw_config = dict(self.config[section])
        
        # 创建新字典并进行类型转换
        config = {}
        config.update(raw_config)  # 先复制所有原始值
        
        # 类型转换
        config['listen_port'] = int(raw_config.get('listen_port', '1161'))
        config['startup_delay'] = int(raw_config.get('startup_delay', '2'))
        config['error_value'] = int(raw_config.get('error_value', '-99998'))
        
        return config
    
    def get_modbus_tcp_config(self) -> Dict[str, Any]:
        """获取 Modbus TCP 配置"""
        section = 'MODBUS_TCP_CONFIG'
        if section not in self.config:
            raise ValueError(f"配置文件中缺少 [{section}] 部分")
        
        raw_config = dict(self.config[section])
        
        # 创建新字典并进行类型转换
        config = {}
        config.update(raw_config)  # 先复制所有原始值
        
        # 类型转换
        config['port'] = int(raw_config.get('port', '502'))
        config['timeout'] = int(raw_config.get('timeout', '3'))
        config['retry_interval'] = int(raw_config.get('retry_interval', '10'))
        config['update_interval'] = int(raw_config.get('update_interval', '5'))
        
        return config
    
    def get_modbus_rtu_config(self) -> Dict[str, Any]:
        """获取 Modbus RTU 配置"""
        section = 'MODBUS_RTU_CONFIG'
        if section not in self.config:
            raise ValueError(f"配置文件中缺少 [{section}] 部分")
        
        raw_config = dict(self.config[section])
        
        # 创建新字典并进行类型转换
        config = {}
        config.update(raw_config)  # 先复制所有原始值
        
        # 类型转换
        config['baudrate'] = int(raw_config.get('baudrate', '9600'))
        config['bytesize'] = int(raw_config.get('bytesize', '8'))
        config['stopbits'] = int(raw_config.get('stopbits', '1'))
        config['timeout'] = int(raw_config.get('timeout', '3'))
        config['retry_interval'] = int(raw_config.get('retry_interval', '10'))
        config['update_interval'] = int(raw_config.get('update_interval', '5'))
        
        return config
    
    def get_system_oid_mapping(self) -> List[Dict[str, Any]]:
        """获取系统 OID 映射配置"""
        system_oids = []
        
        # 查找所有 SYSTEM_OID_* 部分
        for section_name in self.config.sections():
            if section_name.startswith('SYSTEM_OID_'):
                section = self.config[section_name]
                oid_config = {
                    'oid': section.get('oid'),
                    'description': section.get('description'),
                    'type': section.get('type'),
                    'snmp_data_type': section.get('snmp_data_type', 'OctetString')
                }
                
                # 如果是固定值类型，添加 value 字段
                if oid_config['type'] == 'fixed_value':
                    value = section.get('value')
                    # 尝试转换为整数，如果失败则保持字符串
                    if value is not None:
                        try:
                            if oid_config['snmp_data_type'] == 'Integer':
                                oid_config['value'] = int(value)
                            else:
                                oid_config['value'] = value
                        except (ValueError, TypeError):
                            oid_config['value'] = value
                    else:
                        oid_config['value'] = ''
                
                system_oids.append(oid_config)
        
        # 按 OID 排序
        system_oids.sort(key=lambda x: x['oid'])
        return system_oids
    
    def get_snmp_oid_mapping(self) -> List[Dict[str, Any]]:
        """获取 SNMP OID 映射配置"""
        snmp_oids = []
        
        # 查找所有 SNMP_OID_* 部分
        for section_name in self.config.sections():
            if section_name.startswith('SNMP_OID_'):
                section = self.config[section_name]
                
                oid_config = {
                    'oid': section.get('oid'),
                    'description': section.get('description'),
                    'snmp_data_type': section.get('snmp_data_type', 'OctetString')
                }
                
                # Modbus 配置
                processing_type = section.get('processing_type')
                if processing_type != 'communication_status':
                    # 需要 Modbus 读取的 OID
                    register_address = section.get('register_address')
                    if register_address:  # 只有当存在寄存器地址时才添加 modbus_config
                        modbus_config = {
                            'register_address': int(register_address, 16),
                            'unit_id': int(section.get('unit_id', '1')),
                            'function_code': int(section.get('function_code', '3')),
                            'data_type': section.get('data_type', 'uint16')
                        }
                        oid_config['modbus_config'] = modbus_config
                
                # 数据处理配置
                data_processing: Dict[str, Any] = {
                    'type': processing_type
                }
                
                if processing_type == 'multiply':
                    coefficient = float(section.get('coefficient', '1.0'))
                    offset = float(section.get('offset', '0.0'))
                    decimal_places = int(section.get('decimal_places', '0'))
                    
                    data_processing['coefficient'] = coefficient
                    data_processing['offset'] = offset
                    data_processing['decimal_places'] = decimal_places
                
                oid_config['data_processing'] = data_processing
                snmp_oids.append(oid_config)
        
        # 按 OID 排序
        snmp_oids.sort(key=lambda x: x['oid'])
        return snmp_oids

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置信息"""
        config = {
            'snmp_bridge': self.get_snmp_bridge_config(),
            'modbus_tcp': self.get_modbus_tcp_config(),
            'modbus_rtu': self.get_modbus_rtu_config(),
            'system_oids': self.get_system_oid_mapping(),
            'snmp_oids': self.get_snmp_oid_mapping()
        }
        
        # 提取常用配置
        bridge_config = config['snmp_bridge']
        config['modbus_type'] = bridge_config['modbus_type']
        config['timezone_config'] = {
            'timezone_offset': bridge_config['timezone_offset'],
            'timezone_name': 'Custom Timezone'
        }
        
        return config

# 全局配置加载器实例和配置缓存
_config_loader = None
_cached_config = None

def get_config_loader(config_file='config.ini') -> ConfigLoader:
    """获取配置加载器实例"""
    global _config_loader
    if _config_loader is None:
        _config_loader = ConfigLoader(config_file)
    return _config_loader

def get_config(force_reload=False) -> Dict[str, Any]:
    """获取配置信息，支持缓存"""
    global _cached_config, _config_loader
    
    if _cached_config is None or force_reload:
        if _config_loader is None:
            _config_loader = ConfigLoader()
        elif force_reload:
            _config_loader.reload_config()
        
        _cached_config = _config_loader.get_all_config()
        logger.debug("✓ 配置信息已更新")
    
    return _cached_config

def reload_config():
    """重新加载配置"""
    global _cached_config
    _cached_config = None
    return get_config(force_reload=True)

# 便捷函数
def get_snmp_bridge_config() -> Dict[str, Any]:
    """获取 SNMP 桥接配置"""
    return get_config()['snmp_bridge']

def get_modbus_config() -> Dict[str, Any]:
    """获取 Modbus 配置"""
    config = get_config()
    modbus_type = config['modbus_type']
    if modbus_type == 'TCP':
        return config['modbus_tcp']
    else:
        return config['modbus_rtu']

def get_system_oids() -> List[Dict[str, Any]]:
    """获取系统 OID 映射"""
    return get_config()['system_oids']

def get_snmp_oids() -> List[Dict[str, Any]]:
    """获取 SNMP OID 映射"""
    return get_config()['snmp_oids']

def get_modbus_type() -> str:
    """获取 Modbus 类型"""
    return get_config()['modbus_type']

def get_timezone_config() -> Dict[str, Any]:
    """获取时区配置"""
    return get_config()['timezone_config']

if __name__ == "__main__":
    """测试配置加载"""
    print("🧪 测试配置加载 (Utils版本)")
    print("=" * 50)
    
    # 测试配置加载
    config = get_config()
    
    print("SNMP 桥接配置:")
    for key, value in config['snmp_bridge'].items():
        print(f"  {key}: {value}")
    
    print(f"\nModbus 类型: {config['modbus_type']}")
    
    print(f"\n系统 OID 数量: {len(config['system_oids'])}")
    print(f"业务 OID 数量: {len(config['snmp_oids'])}")
    
    print(f"\n时区配置: {config['timezone_config']}")
    
    # 测试重新加载
    print("\n🔄 测试配置重新加载...")
    reload_config()
    
    print("\n✅ 配置加载测试完成 (Utils版本)")