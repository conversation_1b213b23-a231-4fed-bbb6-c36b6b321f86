<template>
  <div class="config-management">
    <div class="page-title">
      <el-icon><DocumentEdit /></el-icon>
      配置文件管理
    </div>

    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" class="btn-primary" @click="refreshConfig">
          <el-icon><Refresh /></el-icon>
          刷新配置
        </el-button>
        <el-button type="success" @click="showCreateSectionDialog = true">
          <el-icon><Plus /></el-icon>
          新增配置节
        </el-button>
        <el-upload
          :show-file-list="false"
          :before-upload="handleUploadConfig"
          accept=".ini"
        >
          <el-button type="info">
            <el-icon><Upload /></el-icon>
            上传配置
          </el-button>
        </el-upload>
        <el-button type="warning" @click="downloadConfig">
          <el-icon><Download /></el-icon>
          下载配置
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-text type="info">配置节数量: {{ sectionsList.length }}</el-text>
      </div>
    </div>

    <!-- 配置节列表 -->
    <div class="sections-container" v-loading="configStore.loading">
      <el-empty v-if="sectionsList.length === 0" description="暂无配置数据">
        <el-button type="primary" @click="refreshConfig">重新加载</el-button>
      </el-empty>

      <div v-else class="sections-grid">
        <el-card 
          v-for="section in sectionsList" 
          :key="section.name" 
          class="config-card"
        >
          <template #header>
            <div class="section-header">
              <div class="section-info">
                <h3>{{ section.name }}</h3>
                <el-tag size="small">{{ section.itemCount }} 项配置</el-tag>
              </div>
              <div class="section-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="editSection(section)"
                >
                  <el-icon><Edit /></el-icon>
                  编辑
                </el-button>
                <el-popconfirm
                  title="确定要删除这个配置节吗？"
                  @confirm="deleteSection(section.name)"
                >
                  <template #reference>
                    <el-button type="danger" size="small">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </template>
                </el-popconfirm>
              </div>
            </div>
          </template>

          <div class="section-content">
            <el-table :data="formatSectionItems(section.items)" stripe>
              <el-table-column prop="key" label="配置键" width="200">
                <template #default="{ row }">
                  <el-text class="config-key">{{ row.key }}</el-text>
                </template>
              </el-table-column>
              <el-table-column prop="value" label="配置值">
                <template #default="{ row }">
                  <el-input
                    v-model="row.value"
                    size="small"
                    @blur="updateConfigItem(section.name, row.key, row.value)"
                    :placeholder="'请输入 ' + row.key + ' 的值'"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-popconfirm
                    title="确定要删除这个配置项吗？"
                    @confirm="deleteConfigItem(section.name, row.key)"
                  >
                    <template #reference>
                      <el-button type="danger" size="small" text>
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </template>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>

            <div class="add-item-form" v-if="section.name === expandedSection">
              <el-divider>添加新配置项</el-divider>
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-input
                    v-model="newItem.key"
                    placeholder="配置键"
                    size="small"
                  />
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="newItem.value"
                    placeholder="配置值"
                    size="small"
                  />
                </el-col>
                <el-col :span="4">
                  <el-button
                    type="primary"
                    size="small"
                    @click="addConfigItem(section.name)"
                    :disabled="!newItem.key || !newItem.value"
                  >
                    添加
                  </el-button>
                </el-col>
              </el-row>
            </div>

            <div class="section-footer">
              <el-button 
                text 
                type="primary" 
                @click="toggleExpandSection(section.name)"
              >
                <el-icon><Plus /></el-icon>
                添加配置项
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 创建配置节对话框 -->
    <el-dialog
      v-model="showCreateSectionDialog"
      title="创建新配置节"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="newSection"
        :rules="sectionRules"
        ref="sectionFormRef"
        label-width="120px"
      >
        <el-form-item label="配置节名称" prop="name">
          <el-input
            v-model="newSection.name"
            placeholder="请输入配置节名称，如: SNMP_OID_5"
          />
        </el-form-item>
        <el-form-item label="初始配置项">
          <div class="initial-items">
            <div 
              v-for="(item, index) in newSection.items" 
              :key="index"
              class="item-row"
            >
              <el-row :gutter="16">
                <el-col :span="10">
                  <el-input
                    v-model="item.key"
                    placeholder="配置键"
                    size="small"
                  />
                </el-col>
                <el-col :span="12">
                  <el-input
                    v-model="item.value"
                    placeholder="配置值"
                    size="small"
                  />
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click="removeInitialItem(index)"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </el-col>
              </el-row>
            </div>
            <el-button
              type="primary"
              text
              @click="addInitialItem"
              class="add-item-btn"
            >
              <el-icon><Plus /></el-icon>
              添加配置项
            </el-button>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateSectionDialog = false">取消</el-button>
        <el-button type="primary" @click="createSection">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useConfigStore } from '@/stores'
import { configApi } from '@/utils/api'

const configStore = useConfigStore()

// 响应式数据
const showCreateSectionDialog = ref(false)
const expandedSection = ref('')
const sectionFormRef = ref()

const newSection = ref({
  name: '',
  items: [{ key: '', value: '' }]
})

const newItem = ref({
  key: '',
  value: ''
})

// 表单验证规则
const sectionRules = {
  name: [
    { required: true, message: '请输入配置节名称', trigger: 'blur' },
    { 
      pattern: /^[A-Z][A-Z0-9_]*$/, 
      message: '配置节名称必须以大写字母开头，只能包含大写字母、数字和下划线', 
      trigger: 'blur' 
    }
  ]
}

// 计算属性
const sectionsList = computed(() => configStore.getSectionsList)

// 方法
const refreshConfig = async () => {
  try {
    await configStore.loadConfig()
    ElMessage.success('配置刷新成功')
  } catch (error) {
    ElMessage.error('配置刷新失败')
  }
}

const formatSectionItems = (items) => {
  return Object.entries(items || {}).map(([key, value]) => ({
    key,
    value
  }))
}

const updateConfigItem = async (section, key, value) => {
  try {
    await configStore.updateConfigItem(section, key, value)
    ElMessage.success(`${section}.${key} 更新成功`)
  } catch (error) {
    ElMessage.error(`${section}.${key} 更新失败`)
  }
}

const deleteConfigItem = async (section, key) => {
  try {
    await configStore.deleteConfigItem(section, key)
    ElMessage.success(`配置项 ${section}.${key} 删除成功`)
  } catch (error) {
    ElMessage.error(`配置项 ${section}.${key} 删除失败`)
  }
}

const deleteSection = async (sectionName) => {
  try {
    await configStore.deleteConfigSection(sectionName)
    ElMessage.success(`配置节 ${sectionName} 删除成功`)
  } catch (error) {
    ElMessage.error(`配置节 ${sectionName} 删除失败`)
  }
}

const toggleExpandSection = (sectionName) => {
  if (expandedSection.value === sectionName) {
    expandedSection.value = ''
  } else {
    expandedSection.value = sectionName
    newItem.value = { key: '', value: '' }
  }
}

const addConfigItem = async (sectionName) => {
  try {
    await configStore.updateConfigItem(sectionName, newItem.value.key, newItem.value.value)
    ElMessage.success('配置项添加成功')
    newItem.value = { key: '', value: '' }
    expandedSection.value = ''
  } catch (error) {
    ElMessage.error('配置项添加失败')
  }
}

const editSection = (section) => {
  ElMessageBox.alert(
    `当前选择的配置节: ${section.name}\n配置项数量: ${section.itemCount}`,
    '配置节信息',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  )
}

const addInitialItem = () => {
  newSection.value.items.push({ key: '', value: '' })
}

const removeInitialItem = (index) => {
  if (newSection.value.items.length > 1) {
    newSection.value.items.splice(index, 1)
  }
}

const createSection = async () => {
  try {
    await sectionFormRef.value.validate()
    
    // 构造配置节数据
    const sectionData = {
      name: newSection.value.name,
      items: {}
    }
    
    // 添加有效的配置项
    newSection.value.items.forEach(item => {
      if (item.key && item.value) {
        sectionData.items[item.key] = item.value
      }
    })
    
    await configStore.createConfigSection(sectionData)
    ElMessage.success(`配置节 ${newSection.value.name} 创建成功`)
    
    // 重置表单
    newSection.value = {
      name: '',
      items: [{ key: '', value: '' }]
    }
    showCreateSectionDialog.value = false
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  }
}

const downloadConfig = async () => {
  try {
    const response = await configApi.downloadConfig()
    
    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/octet-stream' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'config.ini'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('配置文件下载成功')
  } catch (error) {
    ElMessage.error('配置文件下载失败')
  }
}

const handleUploadConfig = async (file) => {
  try {
    await configStore.uploadConfig(file)
    ElMessage.success('配置文件上传成功')
    return false // 阻止默认上传
  } catch (error) {
    ElMessage.error('配置文件上传失败')
    return false
  }
}

// 生命周期
onMounted(() => {
  refreshConfig()
})
</script>

<style scoped>
.config-management {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sections-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  min-height: 400px;
}

.sections-grid {
  display: grid;
  gap: 20px;
  padding-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-info h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 18px;
}

.section-actions {
  display: flex;
  gap: 8px;
}

.section-content {
  padding: 0;
}

.config-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #6366f1;
}

.add-item-form {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.section-footer {
  margin-top: 12px;
  text-align: center;
}

.initial-items {
  width: 100%;
}

.item-row {
  margin-bottom: 12px;
}

.add-item-btn {
  width: 100%;
  margin-top: 8px;
  border: 2px dashed #d1d5db;
  background: #f9fafb;
}

.add-item-btn:hover {
  border-color: #6366f1;
  background: #eef2ff;
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>