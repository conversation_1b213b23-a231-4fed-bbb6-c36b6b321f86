import { defineComponent, toRef, unref, openBlock, createElement<PERSON>lock, mergeProps, Fragment, renderList, renderSlot, createVNode, normalizeClass, createBlock, createCommentVNode, normalizeProps } from 'vue';
import { skeletonProps } from './skeleton.mjs';
import SkeletonItem from './skeleton-item2.mjs';
import _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';
import { useThrottleRender } from '../../../hooks/use-throttle-render/index.mjs';
import { useNamespace } from '../../../hooks/use-namespace/index.mjs';

const __default__ = defineComponent({
  name: "ElSkeleton"
});
const _sfc_main = /* @__PURE__ */ defineComponent({
  ...__default__,
  props: skeletonProps,
  setup(__props, { expose }) {
    const props = __props;
    const ns = useNamespace("skeleton");
    const uiLoading = useThrottleRender(toRef(props, "loading"), props.throttle);
    expose({
      uiLoading
    });
    return (_ctx, _cache) => {
      return unref(uiLoading) ? (openBlock(), createElementBlock("div", mergeProps({
        key: 0,
        class: [unref(ns).b(), unref(ns).is("animated", _ctx.animated)]
      }, _ctx.$attrs), [
        (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.count, (i) => {
          return openBlock(), createElementBlock(Fragment, { key: i }, [
            unref(uiLoading) ? renderSlot(_ctx.$slots, "template", { key: i }, () => [
              createVNode(SkeletonItem, {
                class: normalizeClass(unref(ns).is("first")),
                variant: "p"
              }, null, 8, ["class"]),
              (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.rows, (item) => {
                return openBlock(), createBlock(SkeletonItem, {
                  key: item,
                  class: normalizeClass([
                    unref(ns).e("paragraph"),
                    unref(ns).is("last", item === _ctx.rows && _ctx.rows > 1)
                  ]),
                  variant: "p"
                }, null, 8, ["class"]);
              }), 128))
            ]) : createCommentVNode("v-if", true)
          ], 64);
        }), 128))
      ], 16)) : renderSlot(_ctx.$slots, "default", normalizeProps(mergeProps({ key: 1 }, _ctx.$attrs)));
    };
  }
});
var Skeleton = /* @__PURE__ */ _export_sfc(_sfc_main, [["__file", "skeleton.vue"]]);

export { Skeleton as default };
//# sourceMappingURL=skeleton2.mjs.map
