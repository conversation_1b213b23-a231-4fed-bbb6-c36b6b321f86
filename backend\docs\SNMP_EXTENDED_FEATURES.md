# SNMP 扩展功能说明

本文档描述了 SNMP-Modbus 桥接服务新增的扩展功能。

## 🚀 新增功能概览

### 1. SNMP GETBULK 支持
- **功能**：批量获取多个 OID 值，提高查询效率
- **实现**：在 `snmp_callback` 函数中添加了对 `GetBulkRequestPDU` 的处理
- **参数**：
  - `non_repeaters`：前 N 个 OID 只返回一个值
  - `max_repetitions`：剩余 OID 每个最多返回 M 个值

### 2. SNMP WALK 功能
- **功能**：遍历指定 OID 子树下的所有节点
- **实现**：基于现有的 GETNEXT 逻辑，实现完整的树遍历
- **特点**：
  - 自动检测子树边界
  - 支持最大结果数量限制
  - 防止无限循环

### 3. SNMP SUBTREE 功能
- **功能**：获取指定 OID 前缀下的所有子节点
- **实现**：深度遍历指定 OID 子树
- **特点**：
  - 支持深度限制
  - 支持结果数量限制
  - 高效的子树匹配算法

## 🔧 技术实现

### 核心函数

#### 1. `snmp_walk_subtree(start_oid, max_results=100)`
```python
def snmp_walk_subtree(start_oid, max_results=100):
    """执行 SNMP walk 操作，遍历指定 OID 子树下的所有节点"""
    # 实现逻辑...
```

#### 2. `is_oid_in_subtree(oid, subtree_root)`
```python
def is_oid_in_subtree(oid, subtree_root):
    """检查 OID 是否在指定的子树内"""
    # 实现逻辑...
```

#### 3. `get_oid_subtree(root_oid, max_depth=10, max_results=100)`
```python
def get_oid_subtree(root_oid, max_depth=10, max_results=100):
    """获取指定 OID 的完整子树"""
    # 实现逻辑...
```

### GETBULK 处理逻辑

在 `snmp_callback` 函数中新增的 GETBULK 处理：

```python
elif req_pdu.isSameTypeWith(p_mod.GetBulkRequestPDU()):
    logger.debug("🔍 处理 GETBULK 请求")
    
    # 获取 GETBULK 参数
    non_repeaters = p_mod.apiPDU.get_non_repeaters(req_pdu)
    max_repetitions = p_mod.apiPDU.get_max_repetitions(req_pdu)
    
    # 处理 non-repeaters 和 repeaters
    # 实现逻辑...
```

## 🧪 测试工具

### 1. 快速测试工具 (`snmp_test_tool.py`)

```bash
# 基本用法
python snmp_test_tool.py <command> <oid> [options]

# 示例
python snmp_test_tool.py get .*******.*******.0
python snmp_test_tool.py walk .*******.2.1.1 --max-results 10
python snmp_test_tool.py bulk .*******.2.1.1 --max-repetitions 5
python snmp_test_tool.py subtree .*******.4.1.41475
```

### 2. 完整测试套件 (`test_snmp_extended.py`)

```bash
# 运行完整的功能测试
python test_snmp_extended.py
```

包含以下测试：
- SNMP GET 功能测试
- SNMP GETNEXT 功能测试
- SNMP BULK 功能测试
- SNMP WALK 功能测试
- 性能测试

## 📊 性能优化

### 1. 批量操作
- GETBULK 支持一次请求获取多个 OID
- 减少网络往返次数
- 提高查询效率

### 2. 智能遍历
- WALK 和 SUBTREE 使用高效的二分查找
- 自动边界检测，避免无效查询
- 支持结果数量限制，防止内存溢出

### 3. 错误处理
- 完善的错误检测和处理
- 详细的日志记录
- 优雅的异常恢复

## 🔍 使用示例

### 命令行工具示例

```bash
# 获取系统信息
snmpget -v2c -c public 127.0.0.1:1161 .*******.*******.0

# 遍历系统信息子树
snmpwalk -v2c -c public 127.0.0.1:1161 .*******.2.1.1

# 批量获取多个 OID
snmpbulkwalk -v2c -c public 127.0.0.1:1161 .*******.2.1.1

# 使用内置测试工具
python snmp_test_tool.py walk .*******.2.1.1
python snmp_test_tool.py bulk .*******.2.1.1 --max-repetitions 10
```

### Python 客户端示例

```python
from pysnmp.hlapi import *

# SNMP WALK
for (errorIndication, errorStatus, errorIndex, varBinds) in nextCmd(
    SnmpEngine(),
    CommunityData('public'),
    UdpTransportTarget(('127.0.0.1', 1161)),
    ContextData(),
    ObjectType(ObjectIdentity('.*******.2.1.1')),
    lexicographicMode=False):
    
    if errorIndication:
        break
    elif errorStatus:
        break
    else:
        for varBind in varBinds:
            print(f'{varBind[0]} = {varBind[1]}')

# SNMP BULK
for (errorIndication, errorStatus, errorIndex, varBinds) in bulkCmd(
    SnmpEngine(),
    CommunityData('public'),
    UdpTransportTarget(('127.0.0.1', 1161)),
    ContextData(),
    0, 10,  # non_repeaters=0, max_repetitions=10
    ObjectType(ObjectIdentity('.*******.2.1.1')),
    lexicographicMode=False):
    
    if errorIndication:
        break
    elif errorStatus:
        break
    else:
        for varBind in varBinds:
            print(f'{varBind[0]} = {varBind[1]}')
        break
```

## 🔧 配置说明

新功能无需额外配置，完全兼容现有的配置文件格式。所有扩展功能都基于现有的 OID 映射配置工作。

## 📝 注意事项

1. **性能考虑**：BULK 和 WALK 操作可能返回大量数据，建议合理设置 `max_results` 参数
2. **网络限制**：UDP 包大小限制可能影响 BULK 操作的最大返回数量
3. **兼容性**：所有新功能都向后兼容，不影响现有的 GET 和 GETNEXT 操作
4. **错误处理**：新功能继承了原有的错误代码体系（-99997, -99998）

## 🎯 未来扩展

1. **缓存机制**：为频繁查询的 OID 添加缓存支持
2. **并发优化**：支持并发 Modbus 读取，提高 BULK 操作性能
3. **过滤功能**：支持基于条件的 OID 过滤
4. **统计信息**：添加查询统计和性能监控功能
