"""
SNMP-Modbus Bridge V2 - Core Module
核心模块包，包含SNMP处理、Modbus客户端、数据处理等核心功能
"""

__version__ = "2.0.0"
__author__ = "SNMP-Modbus Bridge Team"

# 尝试绝对导入，如果失败则跳过
try:
    from core.snmp_handler import Async<PERSON>MPHandler
except ImportError:
    pass

try:
    from core.modbus_client import AsyncModbusConnectionPool
except ImportError:
    pass

try:
    from core.data_processor import DataProcessor, SimpleDataProcessor
except ImportError:
    pass

try:
    from core.oid_manager import OIDManager, SimpleOIDHandler
except ImportError:
    pass

__all__ = [
    'AsyncSNMPHandler',
    'AsyncModbusConnectionPool',
    'DataProcessor',
    'SimpleDataProcessor',
    'OIDManager',
    'SimpleOIDHandler'
]