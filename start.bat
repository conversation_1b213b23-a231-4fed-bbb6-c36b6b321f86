@echo off
chcp 65001 >nul
title SNMP-Modbus Bridge Web配置管理工具 - 完整环境

echo 🚀 启动SNMP-Modbus Bridge Web配置管理工具（前后端）
echo ==================================================

rem 检查Node.js环境
echo 📋 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH环境变量
    echo 请安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

rem 检查npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm未安装
    pause
    exit /b 1
)

rem 检查Python环境
echo 📋 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH环境变量
    pause
    exit /b 1
)

rem 创建日志目录
if not exist logs mkdir logs

rem 安装前端依赖
echo 📦 检查前端依赖...
cd frontend
if not exist node_modules (
    echo 安装前端依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 前端依赖安装完成
) else (
    echo ✅ 前端依赖已存在
)
cd ..

rem 安装后端依赖
echo 📦 检查后端依赖...
cd backend
if exist requirements.txt (
    pip install -r requirements.txt >nul 2>&1
    echo ✅ 后端依赖检查完成
) else (
    echo ⚠️  requirements.txt 文件不存在，跳过依赖安装
)
cd ..

rem 启动后端服务
echo 🔧 启动后端API服务（包含SNMP桥接服务）...
cd backend
start "后端服务" cmd /k "python main.py"
cd ..

rem 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

rem 启动前端服务
echo 🔧 启动前端开发服务器...
cd frontend
start "前端服务" cmd /k "npm run dev"
cd ..

echo ✅ 服务启动完成！
echo.
echo ==========================================
echo   访问信息
echo ==========================================
echo.
echo 前端应用: http://localhost:3000
echo 后端API: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo.
echo 默认登录信息:
echo 用户名: admin
echo 密码: admin1234
echo.
echo ==========================================
echo.
echo 提示: 两个服务窗口已启动，请不要关闭
echo 要停止服务，请关闭对应的命令行窗口
echo.
pause