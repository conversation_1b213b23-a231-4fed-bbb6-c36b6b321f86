<template>
  <div class="log-viewer">
    <div class="page-title">
      <el-icon><Document /></el-icon>
      日志查看器
    </div>

    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" class="btn-primary" @click="refreshLogs">
          <el-icon><Refresh /></el-icon>
          刷新日志
        </el-button>
        <el-button type="success" @click="autoRefresh = !autoRefresh">
          <el-icon><Timer /></el-icon>
          {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
        </el-button>
        <el-button type="warning" @click="clearSearch">
          <el-icon><Close /></el-icon>
          清除筛选
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-text type="info">
          自动刷新: {{ autoRefresh ? '开启' : '关闭' }}
          <span v-if="autoRefresh"> ({{ countdown }}s)</span>
        </el-text>
      </div>
    </div>

    <!-- 日志控制面板 -->
    <el-card class="control-panel">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="日志文件">
            <el-select
              v-model="selectedLogFile"
              placeholder="选择日志文件"
              @change="handleLogFileChange"
              style="width: 100%"
            >
              <el-option
                v-for="file in logStore.availableLogFiles"
                :key="file.value"
                :label="file.label"
                :value="file.value"
              >
                <div class="log-file-option">
                  <span>{{ file.label }}</span>
                  <el-text type="info" size="small">{{ formatFileSize(file.size) }}</el-text>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="显示行数">
            <el-input-number
              v-model="logParams.lines"
              :min="10"
              :max="10000"
              :step="10"
              @change="loadCurrentLog"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="搜索关键字">
            <el-input
              v-model="logParams.search"
              placeholder="输入关键字进行搜索"
              clearable
              @keyup.enter="loadCurrentLog"
              @clear="loadCurrentLog"
            >
              <template #suffix>
                <el-button type="primary" @click="loadCurrentLog" size="small" text>
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="日志级别">
            <el-select
              v-model="logParams.level"
              placeholder="选择日志级别"
              clearable
              @change="loadCurrentLog"
              style="width: 100%"
            >
              <el-option label="全部" value="" />
              <el-option label="DEBUG" value="DEBUG" />
              <el-option label="INFO" value="INFO" />
              <el-option label="WARNING" value="WARNING" />
              <el-option label="ERROR" value="ERROR" />
              <el-option label="CRITICAL" value="CRITICAL" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="4">
          <el-form-item label="操作">
            <div class="action-buttons">
              <el-button type="primary" @click="loadCurrentLog" size="small">
                <el-icon><Search /></el-icon>
              </el-button>
              <el-button type="success" @click="downloadLog" size="small">
                <el-icon><Download /></el-icon>
              </el-button>
              <el-button type="warning" @click="exportLog" size="small">
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 日志统计信息 -->
    <el-row :gutter="20" class="log-stats">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon size="24" color="#409eff"><Document /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ logStore.currentLog.total }}</div>
              <div class="stat-label">日志行数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon size="24" color="#67c23a"><Timer /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ lastUpdateTime }}</div>
              <div class="stat-label">最后更新</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon size="24" color="#e6a23c"><Folder /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ selectedLogFile || '未选择' }}</div>
              <div class="stat-label">当前文件</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <el-icon size="24" color="#f56c6c"><Search /></el-icon>
            <div class="stat-info">
              <div class="stat-value">{{ logParams.search ? '已过滤' : '无过滤' }}</div>
              <div class="stat-label">搜索状态</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 日志内容显示 -->
    <el-card class="log-content-card" v-loading="logStore.loading">
      <template #header>
        <div class="log-header">
          <span>日志内容</span>
          <div class="header-actions">
            <el-button size="small" @click="scrollToTop">
              <el-icon><Top /></el-icon>
              顶部
            </el-button>
            <el-button size="small" @click="scrollToBottom">
              <el-icon><Bottom /></el-icon>
              底部
            </el-button>
          </div>
        </div>
      </template>

      <div class="log-container" ref="logContainer">
        <div v-if="filteredLogLines.length === 0" class="empty-log">
          <el-empty description="暂无日志数据">
            <el-button type="primary" @click="loadCurrentLog">重新加载</el-button>
          </el-empty>
        </div>
        <div v-else class="log-content">
          <div
            v-for="(line, index) in filteredLogLines"
            :key="index"
            class="log-line"
            :class="getLogLineClass(line)"
          >
            <span class="line-number">{{ index + 1 }}</span>
            <span class="line-content" v-html="highlightSearchTerm(line)"></span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Document,
  Refresh,
  Timer,
  Close,
  Search,
  Download,
  DocumentCopy,
  Folder,
  Top,
  Bottom
} from '@element-plus/icons-vue'
import { useLogStore } from '@/stores'

const logStore = useLogStore()

// 响应式数据
const selectedLogFile = ref('')
const logParams = ref({
  lines: 100,
  search: '',
  level: ''
})
const autoRefresh = ref(false)
const countdown = ref(30)
const lastUpdateTime = ref('')
const logContainer = ref()

let refreshTimer = null
let countdownTimer = null

// 计算属性
const filteredLogLines = computed(() => {
  let lines = logStore.currentLog.lines || []
  
  // 过滤空行
  lines = lines.filter(line => line.trim() !== '')
  
  // 根据日志级别过滤
  if (logParams.value.level) {
    lines = lines.filter(line => 
      line.toUpperCase().includes(logParams.value.level.toUpperCase())
    )
  }
  
  return lines
})

// 方法
const refreshLogs = async () => {
  try {
    await logStore.loadLogFiles()
    if (selectedLogFile.value) {
      await loadCurrentLog()
    }
    ElMessage.success('日志刷新成功')
  } catch (error) {
    ElMessage.error('日志刷新失败')
  }
}

const handleLogFileChange = async () => {
  if (selectedLogFile.value) {
    await loadCurrentLog()
  }
}

const loadCurrentLog = async () => {
  if (!selectedLogFile.value) {
    ElMessage.warning('请先选择日志文件')
    return
  }

  try {
    await logStore.readLog(selectedLogFile.value, logParams.value)
    lastUpdateTime.value = new Date().toLocaleTimeString('zh-CN')
  } catch (error) {
    ElMessage.error('加载日志失败')
  }
}

const clearSearch = () => {
  logParams.value.search = ''
  logParams.value.level = ''
  loadCurrentLog()
}

const downloadLog = async () => {
  if (!selectedLogFile.value) {
    ElMessage.warning('请先选择日志文件')
    return
  }

  try {
    await logStore.downloadLog(selectedLogFile.value)
    ElMessage.success('日志下载成功')
  } catch (error) {
    ElMessage.error('日志下载失败')
  }
}

const exportLog = () => {
  if (filteredLogLines.value.length === 0) {
    ElMessage.warning('暂无日志内容可导出')
    return
  }

  const content = filteredLogLines.value.join('\n')
  const blob = new Blob([content], { type: 'text/plain' })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${selectedLogFile.value}_export_${new Date().getTime()}.txt`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
  
  ElMessage.success('日志导出成功')
}

const getLogLineClass = (line) => {
  const upperLine = line.toUpperCase()
  if (upperLine.includes('ERROR') || upperLine.includes('CRITICAL')) {
    return 'log-error'
  } else if (upperLine.includes('WARNING') || upperLine.includes('WARN')) {
    return 'log-warning'
  } else if (upperLine.includes('INFO')) {
    return 'log-info'
  } else if (upperLine.includes('DEBUG')) {
    return 'log-debug'
  }
  return ''
}

const highlightSearchTerm = (line) => {
  if (!logParams.value.search) {
    return line
  }
  
  const regex = new RegExp(`(${logParams.value.search})`, 'gi')
  return line.replace(regex, '<mark>$1</mark>')
}

const scrollToTop = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = 0
  }
}

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight
  }
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }

  countdown.value = 30
  
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      countdown.value = 30
      loadCurrentLog()
    }
  }, 1000)
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 监听自动刷新状态变化
const toggleAutoRefresh = () => {
  if (autoRefresh.value) {
    startAutoRefresh()
    ElNotification({
      title: '自动刷新',
      message: '已开启自动刷新，每30秒更新一次',
      type: 'success',
      duration: 2000
    })
  } else {
    stopAutoRefresh()
    ElNotification({
      title: '自动刷新',
      message: '已停止自动刷新',
      type: 'info',
      duration: 2000
    })
  }
}

// 监听自动刷新变化
const unwatchAutoRefresh = ref()
onMounted(() => {
  unwatchAutoRefresh.value = () => {
    if (autoRefresh.value) {
      startAutoRefresh()
    } else {
      stopAutoRefresh()
    }
  }
})

// 生命周期
onMounted(async () => {
  await refreshLogs()
  
  // 如果有日志文件，选择第一个
  if (logStore.availableLogFiles.length > 0) {
    selectedLogFile.value = logStore.availableLogFiles[0].value
    await loadCurrentLog()
  }
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.log-viewer {
  padding: 0;
}

.control-panel {
  margin-bottom: 20px;
}

.log-file-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.log-stats {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
}

.log-content-card {
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.log-container {
  max-height: 600px;
  overflow-y: auto;
  background: #1e1e1e;
  border-radius: 6px;
  padding: 16px;
}

.empty-log {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 6px;
}

.log-content {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.6;
}

.log-line {
  display: flex;
  color: #d4d4d4;
  margin-bottom: 2px;
  padding: 2px 0;
  border-radius: 3px;
}

.log-line:hover {
  background: rgba(255, 255, 255, 0.05);
}

.line-number {
  width: 60px;
  text-align: right;
  color: #858585;
  margin-right: 16px;
  flex-shrink: 0;
  user-select: none;
}

.line-content {
  flex: 1;
  word-break: break-all;
}

/* 日志级别颜色 */
.log-error {
  color: #f85149;
  background: rgba(248, 81, 73, 0.1);
}

.log-warning {
  color: #d29922;
  background: rgba(210, 153, 34, 0.1);
}

.log-info {
  color: #3fb950;
}

.log-debug {
  color: #79c0ff;
}

/* 搜索高亮 */
:deep(mark) {
  background: #ffd700;
  color: #000;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: #2d2d2d;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #777;
}

@media (max-width: 768px) {
  .control-panel .el-col {
    margin-bottom: 15px;
  }
  
  .log-stats .el-col {
    margin-bottom: 10px;
  }
  
  .log-container {
    max-height: 400px;
  }
  
  .log-line {
    flex-direction: column;
  }
  
  .line-number {
    width: auto;
    margin-right: 0;
    margin-bottom: 4px;
  }
}
</style>