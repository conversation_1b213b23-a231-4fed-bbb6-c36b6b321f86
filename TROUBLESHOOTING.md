# 🔧 故障排除指南

## 问题: 页面没有显示

### 问题描述
当您看到后端API服务已经启动（显示"Application startup complete"），但浏览器中无法看到Web界面时，这是因为只启动了后端服务，还需要启动前端应用。

### 解决方案

#### 🚀 方案1: 使用一键启动脚本（推荐）

**Windows用户:**
```cmd
# 停止当前仅后端的服务（按Ctrl+C）
# 然后运行完整启动脚本
cd web-config-manager
start.bat
```

**Linux/Mac用户:**
```bash
# 停止当前仅后端的服务（按Ctrl+C）
# 然后运行完整启动脚本
cd web-config-manager
./start.sh
```

#### 🔧 方案2: 手动分别启动

如果一键启动脚本有问题，可以手动分别启动：

**1. 启动后端服务（终端1）:**
```bash
cd web-config-manager/backend
python main.py
```

**2. 启动前端服务（终端2）:**
```bash
cd web-config-manager/frontend
npm install  # 首次运行时需要安装依赖
npm run dev
```

### 预期结果

正确启动后，您应该看到：

1. **后端服务** (http://localhost:8000)
   - API服务正常运行
   - SNMP桥接服务自动启动
   - API文档可访问: http://localhost:8000/docs

2. **前端应用** (http://localhost:3000)
   - Web管理界面
   - 登录页面
   - 配置管理功能

### 访问信息

- **Web管理界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **默认登录**: admin / admin1234

### 常见问题

#### Q1: 前端启动失败，提示"npm: command not found"
**A:** 需要安装Node.js和npm
- 下载: https://nodejs.org/
- 安装后重新运行启动脚本

#### Q2: 后端启动失败，提示模块缺失
**A:** 安装Python依赖
```bash
cd web-config-manager/backend
pip install -r requirements.txt
```

#### Q3: 端口被占用
**A:** 检查端口占用情况
```bash
# Windows
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# Linux/Mac
lsof -i :3000
lsof -i :8000
```

#### Q4: 无法访问API
**A:** 检查CORS设置和代理配置
- 确保后端CORS配置正确
- 确认前端proxy配置指向正确的后端地址

### 系统架构

```
浏览器 (http://localhost:3000)
    ↓
前端 Vue.js 应用 (端口3000)
    ↓ API代理 (/api -> http://localhost:8000)
后端 FastAPI 服务 (端口8000)
    ↓ 自动启动
SNMP-Modbus桥接服务 (snmp-modbus-bridgev2.py)
```

### 开发调试

如果需要调试，可以查看日志：

**后端日志:**
```bash
tail -f web-config-manager/logs/backend.log
```

**前端日志:**
```bash
tail -f web-config-manager/logs/frontend.log
```

### 技术支持

如果以上方案都无法解决问题，请：

1. 检查系统环境（Python 3.7+, Node.js 16+）
2. 查看详细错误日志
3. 确认防火墙和网络设置
4. 检查依赖安装是否完整

---

**记住**: 完整的Web配置管理工具需要前后端都运行才能正常使用！