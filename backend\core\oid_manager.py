#!/usr/bin/env python3
"""
OID管理器

管理所有OID映射和处理逻辑，提供高效的OID查找和处理功能。

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import asyncio
import bisect
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.config_manager import OIDMapping
from utils.exceptions import SNMPError
from .data_processor import DataProcessor

logger = logging.getLogger(__name__)

@dataclass
class OIDHandler:
    """OID处理器基础类"""
    oid_tuple: Tuple[int, ...]
    oid_str: str
    mapping: OIDMapping
    data_processor: DataProcessor
    
    def __eq__(self, other):
        if isinstance(other, tuple):
            return self.oid_tuple == other
        return self.oid_tuple == other.oid_tuple
    
    def __lt__(self, other):
        if isinstance(other, tuple):
            return self.oid_tuple < other
        return self.oid_tuple < other.oid_tuple
    
    def __le__(self, other):
        if isinstance(other, tuple):
            return self.oid_tuple <= other
        return self.oid_tuple <= other.oid_tuple
    
    def __gt__(self, other):
        if isinstance(other, tuple):
            return self.oid_tuple > other
        return self.oid_tuple > other.oid_tuple
    
    def __ge__(self, other):
        if isinstance(other, tuple):
            return self.oid_tuple >= other
        return self.oid_tuple >= other.oid_tuple
    
    async def handle_request(self, proto_ver) -> Any:
        """处理SNMP请求"""
        raise NotImplementedError

class SystemOIDHandler(OIDHandler):
    """系统OID处理器"""
    
    def __init__(self, mapping: OIDMapping, data_processor: DataProcessor):
        oid_tuple = tuple(int(x) for x in mapping.oid.strip('.').split('.'))
        super().__init__(oid_tuple, mapping.oid, mapping, data_processor)
        self.start_time = asyncio.get_event_loop().time()
    
    async def handle_request(self, proto_ver) -> Any:
        """处理系统OID请求"""
        try:
            logger.debug(f"处理系统OID请求: {self.mapping.description}")
            
            if self.mapping.system_type == 'fixed_value':
                value = self.mapping.system_value
            elif self.mapping.system_type == 'uptime':
                current_time = asyncio.get_event_loop().time()
                uptime_ticks = int((current_time - self.start_time) * 100)
                value = uptime_ticks
            elif self.mapping.system_type == 'utc_time':
                value = await self.data_processor.get_utc_time()
            else:
                value = "Unknown"
            
            return await self.data_processor.convert_to_snmp_value(
                value, proto_ver, self.mapping.snmp_data_type
            )
            
        except Exception as e:
            logger.error(f"系统OID处理异常 {self.oid_str}: {e}")
            raise SNMPError(f"系统OID处理失败: {e}", self.oid_str)

class ModbusOIDHandler(OIDHandler):
    """Modbus OID处理器"""
    
    def __init__(self, mapping: OIDMapping, data_processor: DataProcessor, 
                 modbus_pool):
        oid_tuple = tuple(int(x) for x in mapping.oid.strip('.').split('.'))
        super().__init__(oid_tuple, mapping.oid, mapping, data_processor)
        self.modbus_pool = modbus_pool
    
    async def handle_request(self, proto_ver) -> Any:
        """处理Modbus OID请求（无缓存版本）
        
        处理流程:
        1. 检查是否为通讯状态OID
        2. 尝试从Modbus读取数据
        3. 根据读取结果返回相应值或错误码
        """
        try:
            logger.debug(f"处理Modbus OID请求: {self.mapping.description}")
            
            # 通讯状态OID直接返回1
            if self.mapping.processing_type == 'communication_status':
                return await self.data_processor.convert_to_snmp_value(
                    1, proto_ver, self.mapping.snmp_data_type
                )
            
            # 直接尝试从 Modbus 读取数据（不预先检查连接状态）
            raw_value = await self._read_modbus_data()
            if raw_value is None:
                # 读取失败（可能是连接失败或读取失败）
                logger.warning(f"Modbus读取失败，返回错误代码: {self.oid_str}")
                return await self.data_processor.convert_to_snmp_value(
                    -99998, proto_ver, self.mapping.snmp_data_type
                )
            
            # 数据处理
            processed_value = await self.data_processor.process_modbus_data(
                raw_value, self.mapping
            )
            
            # 返回处理后的数据
            logger.debug(f"Modbus数据处理成功: {self.oid_str} = {processed_value}")
            return await self.data_processor.convert_to_snmp_value(
                processed_value, proto_ver, self.mapping.snmp_data_type
            )
            
        except Exception as e:
            logger.error(f"Modbus OID处理异常 {self.oid_str}: {e}")
            return await self.data_processor.convert_to_snmp_value(
                -99998, proto_ver, self.mapping.snmp_data_type
            )
    
    async def _read_modbus_data(self) -> Optional[Any]:
        """从Modbus读取数据
        
        返回值说明:
        - 成功: 返回实际读取的数据值
        - 连接失败: 返回None
        - 读取失败: 返回None
        """
        try:
            logger.debug(f"开始读取Modbus数据: OID={self.oid_str}")
            
            # 调用modbus_pool的read_register方法
            result = await self.modbus_pool.read_register(
                address=self.mapping.register_address,
                unit_id=self.mapping.unit_id,
                function_code=self.mapping.function_code
            )
            
            # 注意：read_register方法在成功时直接返回值，失败时抛出异常
            logger.debug(f"Modbus读取成功: OID={self.oid_str}, 值={result}")
            return result
                
        except Exception as e:
            # 所有异常（连接失败、读取失败）都返回None
            logger.error(f"Modbus读取异常: OID={self.oid_str}, 错误={e}")
            return None

class OIDManager:
    """OID管理器"""
    
    def __init__(self, data_processor: DataProcessor, modbus_pool=None):
        self.data_processor = data_processor
        self.modbus_pool = modbus_pool
        self.handlers: List[OIDHandler] = []
        self.handler_index: Dict[Tuple[int, ...], OIDHandler] = {}
        
        logger.info("初始化OID管理器（无缓存模式）")
    
    async def load_oid_mappings(self, oid_mappings: List[OIDMapping]):
        """加载OID映射"""
        self.handlers.clear()
        self.handler_index.clear()
        
        for mapping in oid_mappings:
            try:
                if mapping.oid_type == 'system':
                    handler = SystemOIDHandler(mapping, self.data_processor)
                elif mapping.oid_type == 'modbus':
                    handler = ModbusOIDHandler(
                        mapping, self.data_processor, self.modbus_pool
                    )
                else:
                    logger.warning(f"未知的OID类型: {mapping.oid_type}")
                    continue
                
                self.handlers.append(handler)
                self.handler_index[handler.oid_tuple] = handler
                
                logger.debug(f"加载OID映射: {mapping.oid} -> {mapping.description}")
                
            except Exception as e:
                logger.error(f"加载OID映射失败 {mapping.oid}: {e}")
        
        # 按OID排序
        self.handlers.sort(key=lambda x: x.oid_tuple)
        
        logger.info(f"OID映射加载完成: {len(self.handlers)}个")
    
    def find_handler(self, oid: Tuple[int, ...]) -> Optional[OIDHandler]:
        """查找OID处理器"""
        return self.handler_index.get(oid)
    
    def find_next_handler(self, oid: Tuple[int, ...]) -> Optional[OIDHandler]:
        """查找下一个OID处理器"""
        idx = bisect.bisect_right(self.handlers, oid)
        if idx < len(self.handlers):
            return self.handlers[idx]
        return None
    
    def find_handlers_in_subtree(self, root_oid: Tuple[int, ...], 
                                max_results: int = 100) -> List[OIDHandler]:
        """查找子树中的所有处理器"""
        results = []
        
        for handler in self.handlers:
            if len(results) >= max_results:
                break
            
            if self._is_oid_in_subtree(handler.oid_tuple, root_oid):
                results.append(handler)
        
        return results
    
    def _is_oid_in_subtree(self, oid: Tuple[int, ...], 
                          subtree_root: Tuple[int, ...]) -> bool:
        """检查OID是否在子树内"""
        if len(oid) < len(subtree_root):
            return False
        
        return oid[:len(subtree_root)] == subtree_root
    
    async def handle_get_request(self, oid: Tuple[int, ...], proto_ver) -> Any:
        """处理GET请求"""
        handler = self.find_handler(oid)
        if handler:
            return await handler.handle_request(proto_ver)
        else:
            # 返回未定义OID错误
            return await self.data_processor.convert_to_snmp_value(
                -99997, proto_ver, 'Integer'
            )
    
    async def handle_getnext_request(self, oid: Tuple[int, ...], proto_ver) -> Optional[Tuple[Tuple[int, ...], Any]]:
        """处理GETNEXT请求"""
        handler = self.find_next_handler(oid)
        if handler:
            value = await handler.handle_request(proto_ver)
            return handler.oid_tuple, value
        else:
            # 到达MIB末尾
            return None
    
    async def handle_walk_request(self, start_oid: Tuple[int, ...], 
                                max_results: int = 100) -> List[Tuple[Tuple[int, ...], Any]]:
        """处理WALK请求"""
        results = []
        current_oid = start_oid
        
        for _ in range(max_results):
            handler = self.find_next_handler(current_oid)
            if not handler:
                break
            
            # 检查是否仍在子树内
            if not self._is_oid_in_subtree(handler.oid_tuple, start_oid):
                break
            
            try:
                value = await handler.handle_request(0)  # 使用默认协议版本
                results.append((handler.oid_tuple, value))
                current_oid = handler.oid_tuple
            except Exception as e:
                logger.error(f"WALK请求处理异常 {handler.oid_str}: {e}")
                break
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取OID管理器统计信息"""
        system_count = len([h for h in self.handlers if isinstance(h, SystemOIDHandler)])
        modbus_count = len([h for h in self.handlers if isinstance(h, ModbusOIDHandler)])
        
        return {
            'total_handlers': len(self.handlers),
            'system_oids': system_count,
            'modbus_oids': modbus_count,
            'loaded': len(self.handlers) > 0
        }

# 为兼容性添加简化的OID处理器
class SimpleOIDHandler:
    """简化的OID处理器（兼容现有接口）"""
    
    def __init__(self, oid_config: Dict, data_processor, 
                 modbus_pool, cache=None):
        self.oid_config = oid_config
        self.oid_str = oid_config['oid']
        self.name = tuple(int(x) for x in oid_config['oid'].strip('.').split('.'))
        self.description = oid_config['description']
        self.data_processor = data_processor
        self.modbus_pool = modbus_pool
        self.cache = cache  # 保持接口一致性，在无缓存模式下为None
        self.start_time = time.time()
        
        logger.debug(f"初始化OID处理器（无缓存）: {self.oid_str} - {self.description}")
    
    def __eq__(self, other):
        if isinstance(other, tuple):
            return self.name == other
        return self.name == other.name
    
    def __lt__(self, other):
        if isinstance(other, tuple):
            return self.name < other
        return self.name < other.name
    
    async def handle_request(self, proto_ver: int) -> Any:
        """处理请求"""
        try:
            logger.debug(f"处理OID请求: {self.description}")
            
            # 系统OID
            if 'type' in self.oid_config:
                return await self._handle_system_oid(proto_ver)
            
            # Modbus OID
            else:
                return await self._handle_modbus_oid(proto_ver)
        
        except Exception as e:
            logger.error(f"OID处理异常 {self.oid_str}: {e}")
            return await self.data_processor.convert_to_snmp_value(
                -99998, proto_ver, self.oid_config.get('snmp_data_type', 'OctetString')
            )
    
    async def _handle_system_oid(self, proto_ver: int) -> Any:
        """处理系统OID"""
        oid_type = self.oid_config['type']
        
        if oid_type == 'fixed_value':
            value = self.oid_config['value']
        elif oid_type == 'uptime':
            uptime_ticks = int((time.time() - self.start_time) * 100)
            value = uptime_ticks
        elif oid_type == 'utc_time':
            value = await self.data_processor.get_utc_time()
        else:
            value = "Unknown"
        
        return await self.data_processor.convert_to_snmp_value(
            value, proto_ver, self.oid_config.get('snmp_data_type', 'OctetString')
        )
    
    async def _handle_modbus_oid(self, proto_ver: int) -> Any:
        """处理Modbus OID（无缓存版本）"""
        # 通讯状态OID
        if self.oid_config['data_processing']['type'] == 'communication_status':
            return await self.data_processor.convert_to_snmp_value(
                1, proto_ver, self.oid_config.get('snmp_data_type', 'Integer')
            )
        
        # 检查Modbus连接状态
        if self.modbus_pool and hasattr(self.modbus_pool, 'check_connection_status'):
            is_connected = await self.modbus_pool.check_connection_status()
            if not is_connected:
                logger.warning(f"Modbus连接中断，返回错误代码: {self.oid_str}")
                return await self.data_processor.convert_to_snmp_value(
                    -99998, proto_ver, self.oid_config.get('snmp_data_type', 'OctetString')
                )
        
        # 直接从Modbus读取
        raw_value = await self.modbus_pool.read_register(
            address=self.oid_config['modbus_config']['register_address'],
            unit_id=self.oid_config['modbus_config']['unit_id'],
            function_code=self.oid_config['modbus_config']['function_code']
        )
        
        if raw_value is None:
            logger.error(f"Modbus读取失败: {self.oid_str}")
            return await self.data_processor.convert_to_snmp_value(
                -99998, proto_ver, self.oid_config.get('snmp_data_type', 'OctetString')
            )
        
        # 数据处理
        processed_value = await self.data_processor.process_modbus_data(raw_value, self.oid_config)
        
        # 直接返回处理后的数据
        logger.debug(f"Modbus数据处理成功: {self.oid_str} = {processed_value}")
        return await self.data_processor.convert_to_snmp_value(
            processed_value, proto_ver, self.oid_config.get('snmp_data_type', 'OctetString')
        )