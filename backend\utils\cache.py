#!/usr/bin/env python3
"""
数据缓存机制

实现多层级的数据缓存，减少Modbus查询频率，提高系统响应性能。
支持TTL过期、LRU淘汰、异步更新等功能。

主要功能：
1. TTL缓存 - 时间到期自动失效
2. LRU淘汰 - 最近最少使用算法
3. 异步更新 - 后台异步刷新缓存
4. 批量缓存 - 支持批量操作
5. 缓存统计 - 命中率、性能统计

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import asyncio
import time
import logging
from typing import Any, Dict, Optional, Tuple, List
from dataclasses import dataclass, field
from collections import OrderedDict
import json
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class CacheItem:
    """缓存项数据类"""
    value: Any
    created_at: float = field(default_factory=time.time)
    accessed_at: float = field(default_factory=time.time)
    hit_count: int = 0
    ttl: float = 300.0  # 默认5分钟TTL
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return (time.time() - self.created_at) > self.ttl
    
    def is_stale(self, stale_threshold: float = 0.8) -> bool:
        """检查是否接近过期（用于异步刷新）"""
        age = time.time() - self.created_at
        return age > (self.ttl * stale_threshold)
    
    def touch(self):
        """更新访问时间"""
        self.accessed_at = time.time()
        self.hit_count += 1

@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    expirations: int = 0
    size: int = 0
    max_size: int = 0
    
    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'hits': self.hits,
            'misses': self.misses,
            'evictions': self.evictions,
            'expirations': self.expirations,
            'size': self.size,
            'max_size': self.max_size,
            'hit_rate': self.hit_rate
        }

class DataCache:
    """高性能数据缓存类"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300.0, 
                 cleanup_interval: float = 60.0, enable_async_refresh: bool = True):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存项数量
            default_ttl: 默认TTL时间（秒）
            cleanup_interval: 清理间隔（秒）
            enable_async_refresh: 是否启用异步刷新
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cleanup_interval = cleanup_interval
        self.enable_async_refresh = enable_async_refresh
        
        # 使用OrderedDict实现LRU
        self._cache: OrderedDict[str, CacheItem] = OrderedDict()
        self._lock = asyncio.Lock()
        self._stats = CacheStats(max_size=max_size)
        self._cleanup_task = None
        self._refresh_callbacks = {}  # OID -> 刷新回调函数
        
        logger.info(f"初始化数据缓存，最大容量: {max_size}, 默认TTL: {default_ttl}秒")
    
    def _generate_cache_key(self, oid: str, unit_id: Optional[int] = None, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [oid]
        if unit_id is not None:
            key_parts.append(f"unit_{unit_id}")
        
        # 添加其他参数
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}_{v}")
        
        cache_key = "_".join(key_parts)
        
        # 对于很长的键，使用MD5哈希
        if len(cache_key) > 200:
            cache_key = hashlib.md5(cache_key.encode()).hexdigest()
        
        return cache_key
    
    async def get(self, oid: str, unit_id: Optional[int] = None, **kwargs) -> Optional[Any]:
        """获取缓存值"""
        cache_key = self._generate_cache_key(oid, unit_id, **kwargs)
        
        async with self._lock:
            if cache_key in self._cache:
                item = self._cache[cache_key]
                
                # 检查是否过期
                if item.is_expired():
                    logger.debug(f"缓存过期: {cache_key}")
                    del self._cache[cache_key]
                    self._stats.expirations += 1
                    self._stats.misses += 1
                    self._stats.size = len(self._cache)
                    return None
                
                # 更新访问信息
                item.touch()
                
                # 移到末尾（LRU）
                self._cache.move_to_end(cache_key)
                
                self._stats.hits += 1
                
                logger.debug(f"缓存命中: {cache_key}, 命中次数: {item.hit_count}")
                
                # 检查是否需要异步刷新
                if self.enable_async_refresh and item.is_stale():
                    await self._schedule_async_refresh(cache_key, oid, unit_id, **kwargs)
                
                return item.value
            else:
                self._stats.misses += 1
                logger.debug(f"缓存未命中: {cache_key}")
                return None
    
    async def set(self, oid: str, value: Any, ttl: Optional[float] = None, 
                  unit_id: Optional[int] = None, **kwargs):
        """设置缓存值"""
        cache_key = self._generate_cache_key(oid, unit_id, **kwargs)
        ttl = ttl or self.default_ttl
        
        async with self._lock:
            # 检查容量限制
            if cache_key not in self._cache and len(self._cache) >= self.max_size:
                # 淘汰最旧的项（LRU）
                evicted_key, evicted_item = self._cache.popitem(last=False)
                self._stats.evictions += 1
                logger.debug(f"LRU淘汰: {evicted_key}")
            
            # 创建或更新缓存项
            if cache_key in self._cache:
                # 更新现有项
                item = self._cache[cache_key]
                item.value = value
                item.created_at = time.time()
                item.ttl = ttl
            else:
                # 创建新项
                item = CacheItem(value=value, ttl=ttl)
                self._cache[cache_key] = item
            
            # 移到末尾
            self._cache.move_to_end(cache_key)
            
            self._stats.size = len(self._cache)
            
            logger.debug(f"缓存设置: {cache_key}, TTL: {ttl}秒")
    
    async def delete(self, oid: str, unit_id: Optional[int] = None, **kwargs):
        """删除缓存项"""
        cache_key = self._generate_cache_key(oid, unit_id, **kwargs)
        
        async with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
                self._stats.size = len(self._cache)
                logger.debug(f"缓存删除: {cache_key}")
                return True
            return False
    
    async def clear(self):
        """清空缓存"""
        async with self._lock:
            self._cache.clear()
            self._stats.size = 0
            logger.info("缓存已清空")
    
    async def _schedule_async_refresh(self, cache_key: str, oid: str, 
                                    unit_id: Optional[int] = None, **kwargs):
        """调度异步刷新"""
        if oid in self._refresh_callbacks:
            callback = self._refresh_callbacks[oid]
            
            # 在后台异步刷新
            asyncio.create_task(self._async_refresh(
                cache_key, callback, oid, unit_id, **kwargs
            ))
    
    async def _async_refresh(self, cache_key: str, callback, oid: str, 
                           unit_id: Optional[int] = None, **kwargs):
        """执行异步刷新"""
        try:
            logger.debug(f"开始异步刷新缓存: {cache_key}")
            new_value = await callback(oid, unit_id, **kwargs)
            
            if new_value is not None:
                await self.set(oid, new_value, unit_id=unit_id, **kwargs)
                logger.debug(f"异步刷新完成: {cache_key}")
            
        except Exception as e:
            logger.error(f"异步刷新失败 {cache_key}: {e}")
    
    def register_refresh_callback(self, oid: str, callback):
        """注册刷新回调函数"""
        self._refresh_callbacks[oid] = callback
        logger.debug(f"注册刷新回调: {oid}")
    
    async def start_cleanup_task(self):
        """启动清理任务"""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.info("缓存清理任务已启动")
    
    async def stop_cleanup_task(self):
        """停止清理任务"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
            logger.info("缓存清理任务已停止")
    
    async def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                await asyncio.sleep(self.cleanup_interval)
                await self._cleanup_expired_items()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"缓存清理异常: {e}")
    
    async def _cleanup_expired_items(self):
        """清理过期项"""
        expired_keys = []
        
        async with self._lock:
            for key, item in self._cache.items():
                if item.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self._stats.expirations += 1
            
            if expired_keys:
                self._stats.size = len(self._cache)
                logger.debug(f"清理过期缓存项: {len(expired_keys)}个")
    
    async def get_batch(self, oids: List[str], unit_id: Optional[int] = None, 
                       **kwargs) -> Dict[str, Any]:
        """批量获取缓存值"""
        results = {}
        
        for oid in oids:
            value = await self.get(oid, unit_id, **kwargs)
            if value is not None:
                results[oid] = value
        
        return results
    
    async def set_batch(self, data: Dict[str, Any], ttl: Optional[float] = None, 
                       unit_id: Optional[int] = None, **kwargs):
        """批量设置缓存值"""
        for oid, value in data.items():
            await self.set(oid, value, ttl, unit_id, **kwargs)
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        self._stats.size = len(self._cache)
        return self._stats
    
    async def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存详细信息"""
        async with self._lock:
            cache_items = []
            for key, item in self._cache.items():
                cache_items.append({
                    'key': key,
                    'created_at': item.created_at,
                    'accessed_at': item.accessed_at,
                    'hit_count': item.hit_count,
                    'ttl': item.ttl,
                    'age': time.time() - item.created_at,
                    'is_expired': item.is_expired(),
                    'is_stale': item.is_stale()
                })
            
            return {
                'stats': self.get_stats().to_dict(),
                'items': cache_items,
                'config': {
                    'max_size': self.max_size,
                    'default_ttl': self.default_ttl,
                    'cleanup_interval': self.cleanup_interval,
                    'enable_async_refresh': self.enable_async_refresh
                }
            }

class SimpleCache:
    """简单的TTL缓存实现，兼容原有接口"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300.0):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheItem] = {}
        self._stats = {'hits': 0, 'misses': 0}
        logger.info(f"初始化简单缓存，最大容量: {max_size}, 默认TTL: {default_ttl}秒")
    
    def _generate_key(self, oid: str, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [oid]
        for k, v in sorted(kwargs.items()):
            if v is not None:
                key_parts.append(f"{k}_{v}")
        return "_".join(key_parts)
    
    async def get(self, oid: str, **kwargs) -> Optional[Any]:
        """获取缓存值"""
        key = self._generate_key(oid, **kwargs)
        
        if key in self._cache:
            item = self._cache[key]
            if item.is_expired():
                del self._cache[key]
                self._stats['misses'] += 1
                return None
            else:
                self._stats['hits'] += 1
                return item.value
        else:
            self._stats['misses'] += 1
            return None
    
    async def set(self, oid: str, value: Any, ttl: Optional[float] = None, **kwargs):
        """设置缓存值"""
        key = self._generate_key(oid, **kwargs)
        ttl = ttl or self.default_ttl
        
        # 检查容量限制
        if len(self._cache) >= self.max_size and key not in self._cache:
            # 简单的FIFO清理
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
        
        self._cache[key] = CacheItem(
            value=value,
            created_at=time.time(),
            ttl=ttl
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total = self._stats['hits'] + self._stats['misses']
        hit_rate = self._stats['hits'] / total if total > 0 else 0.0
        
        return {
            'hits': self._stats['hits'],
            'misses': self._stats['misses'],
            'hit_rate': hit_rate,
            'size': len(self._cache),
            'max_size': self.max_size
        }

class ModbusDataCache(DataCache):
    """专门用于Modbus数据的缓存类"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300.0):
        super().__init__(max_size, default_ttl)
        self._communication_status = {}  # 存储通讯状态
        logger.info("初始化Modbus数据缓存")
    
    async def get_modbus_value(self, oid: str, register_address: int, 
                              unit_id: int, function_code: int, 
                              check_communication: bool = True) -> Optional[Any]:
        """获取Modbus缓存值
        
        Args:
            oid: OID字符串
            register_address: 寄存器地址
            unit_id: 单元ID
            function_code: 功能码
            check_communication: 是否检查通讯状态
        
        Returns:
            缓存值或None（通讯中断时返回None）
        """
        # 如果需要检查通讯状态且通讯中断，则不返回缓存值
        if check_communication and not self._is_communication_ok(unit_id):
            logger.debug(f"通讯中断，跳过缓存读取: {oid}")
            return None
        
        return await self.get(
            oid, 
            unit_id=unit_id,
            register=register_address,
            function=function_code
        )
    
    async def set_modbus_value(self, oid: str, value: Any, register_address: int,
                              unit_id: int, function_code: int, ttl: Optional[float] = None):
        """设置Modbus缓存值"""
        # 设置缓存值时更新通讯状态为正常
        self._update_communication_status(unit_id, True)
        
        await self.set(
            oid, 
            value, 
            ttl,
            unit_id=unit_id,
            register=register_address,
            function=function_code
        )
    
    def _update_communication_status(self, unit_id: int, is_ok: bool):
        """更新通讯状态"""
        self._communication_status[unit_id] = {
            'is_ok': is_ok,
            'last_update': time.time()
        }
        status_text = "正常" if is_ok else "中断"
        logger.debug(f"更新通讯状态: 单元ID {unit_id} -> {status_text}")
    
    def _is_communication_ok(self, unit_id: int) -> bool:
        """检查通讯状态"""
        status = self._communication_status.get(unit_id)
        if status is None:
            # 首次访问，假设通讯正常
            return True
        
        # 检查状态是否过期（超过5分钟认为状态过期）
        if time.time() - status['last_update'] > 300:
            # 状态过期，假设通讯正常
            return True
        
        return status['is_ok']
    
    async def mark_communication_error(self, unit_id: int):
        """标记通讯错误"""
        self._update_communication_status(unit_id, False)
        logger.warning(f"标记通讯错误: 单元ID {unit_id}")
    
    async def clear_communication_status(self, unit_id: Optional[int] = None):
        """清除通讯状态"""
        if unit_id is not None:
            self._communication_status.pop(unit_id, None)
            logger.debug(f"清除通讯状态: 单元ID {unit_id}")
        else:
            self._communication_status.clear()
            logger.debug("清除所有通讯状态")
    
    def get_communication_status(self) -> Dict[int, Dict[str, Any]]:
        """获取所有通讯状态"""
        return self._communication_status.copy()
