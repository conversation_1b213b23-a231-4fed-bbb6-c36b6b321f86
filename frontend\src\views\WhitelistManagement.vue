<template>
  <div class="whitelist-management">
    <div class="page-title">
      <el-icon><Shield /></el-icon>
      NMS白名单管理
    </div>

    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" class="btn-primary" @click="refreshWhitelist">
          <el-icon><Refresh /></el-icon>
          刷新配置
        </el-button>
        <el-button type="success" @click="showGlobalConfigDialog = true">
          <el-icon><Setting /></el-icon>
          全局配置
        </el-button>
        <el-button type="warning" @click="testWhitelist">
          <el-icon><Connection /></el-icon>
          测试连接
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-switch
          v-model="whitelistStore.config.enable"
          size="large"
          :active-text="`白名单: ${whitelistStore.config.enable ? '启用' : '禁用'}`"
          @change="toggleWhitelist"
        />
      </div>
    </div>

    <!-- 白名单状态卡片 -->
    <div class="status-cards" v-loading="whitelistStore.loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon">
                <el-icon size="32" color="#67c23a"><CircleCheckFilled /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-value">{{ enabledEntriesCount }}</div>
                <div class="status-label">启用条目</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon">
                <el-icon size="32" color="#e6a23c"><WarningFilled /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-value">{{ disabledEntriesCount }}</div>
                <div class="status-label">禁用条目</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon">
                <el-icon size="32" color="#409eff"><InfoFilled /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-value">{{ whitelistStore.totalEntries }}</div>
                <div class="status-label">总条目数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="status-card">
            <div class="status-content">
              <div class="status-icon">
                <el-icon size="32" :color="policyColor"><Shield /></el-icon>
              </div>
              <div class="status-info">
                <div class="status-value">{{ whitelistStore.config.default_policy }}</div>
                <div class="status-label">默认策略</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 白名单条目列表 -->
    <el-card class="whitelist-table-card">
      <template #header>
        <div class="card-header">
          <span>白名单条目配置</span>
          <el-text type="info">最多支持10个白名单条目</el-text>
        </div>
      </template>

      <el-table :data="whitelistEntries" stripe style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" align="center">
          <template #default="{ row }">
            <el-tag size="small" :type="row.enable ? 'success' : 'info'">
              {{ row.id }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="ip" label="IP地址" min-width="140">
          <template #default="{ row }">
            <el-input
              v-model="row.ip"
              size="small"
              placeholder="请输入IP地址"
              @blur="validateAndSave(row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="mask" label="子网掩码" min-width="140">
          <template #default="{ row }">
            <el-input
              v-model="row.mask"
              size="small"
              placeholder="请输入子网掩码"
              @blur="validateAndSave(row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200">
          <template #default="{ row }">
            <el-input
              v-model="row.description"
              size="small"
              placeholder="请输入描述信息"
              @blur="validateAndSave(row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="网络段" min-width="160">
          <template #default="{ row }">
            <el-text v-if="row.ip && row.mask" class="network-segment">
              {{ getNetworkSegment(row.ip, row.mask) }}
            </el-text>
            <el-text v-else type="info">未配置</el-text>
          </template>
        </el-table-column>

        <el-table-column prop="enable" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.enable"
              size="small"
              @change="updateEntry(row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="testEntry(row)"
              :disabled="!row.ip || !row.mask"
            >
              <el-icon><Connection /></el-icon>
              测试
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 全局配置对话框 -->
    <el-dialog
      v-model="showGlobalConfigDialog"
      title="白名单全局配置"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="globalConfig" label-width="120px">
        <el-form-item label="启用白名单">
          <el-switch v-model="globalConfig.enable" />
          <el-text type="info" style="margin-left: 10px">
            {{ globalConfig.enable ? '启用后将根据白名单规则过滤访问' : '禁用后允许所有IP访问' }}
          </el-text>
        </el-form-item>

        <el-form-item label="默认策略">
          <el-radio-group v-model="globalConfig.default_policy">
            <el-radio label="allow">允许所有</el-radio>
            <el-radio label="deny">拒绝所有</el-radio>
          </el-radio-group>
          <el-text type="info" style="display: block; margin-top: 5px">
            当IP不在白名单中时的处理策略
          </el-text>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showGlobalConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveGlobalConfig">保存配置</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { 
  Shield, 
  Refresh, 
  Setting, 
  Connection,
  CircleCheckFilled,
  WarningFilled,
  InfoFilled
} from '@element-plus/icons-vue'
import { useWhitelistStore } from '@/stores'

const whitelistStore = useWhitelistStore()

// 响应式数据
const showGlobalConfigDialog = ref(false)
const globalConfig = ref({
  enable: true,
  default_policy: 'deny'
})

// 计算属性
const whitelistEntries = computed(() => {
  // 确保有10个条目，如果不足则补充空条目
  const entries = [...whitelistStore.config.entries]
  while (entries.length < 10) {
    entries.push({
      id: entries.length + 1,
      ip: '',
      mask: '',
      description: '',
      enable: false
    })
  }
  return entries.slice(0, 10)
})

const enabledEntriesCount = computed(() => {
  return whitelistStore.enabledEntries.length
})

const disabledEntriesCount = computed(() => {
  return whitelistEntries.value.filter(entry => !entry.enable).length
})

const policyColor = computed(() => {
  return whitelistStore.config.default_policy === 'allow' ? '#67c23a' : '#f56c6c'
})

// 方法
const refreshWhitelist = async () => {
  try {
    await whitelistStore.loadWhitelist()
    globalConfig.value = {
      enable: whitelistStore.config.enable,
      default_policy: whitelistStore.config.default_policy
    }
    ElMessage.success('白名单配置刷新成功')
  } catch (error) {
    ElMessage.error('白名单配置刷新失败')
  }
}

const toggleWhitelist = async (enabled) => {
  try {
    await whitelistStore.updateGlobalConfig({ enable: enabled })
    ElMessage.success(enabled ? '白名单已启用' : '白名单已禁用')
  } catch (error) {
    ElMessage.error('白名单状态切换失败')
    // 恢复状态
    whitelistStore.config.enable = !enabled
  }
}

const validateAndSave = async (entry) => {
  // 如果IP和掩码都为空，不进行保存
  if (!entry.ip && !entry.mask) {
    return
  }

  // 简单的IP地址验证
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  
  if (entry.ip && !ipRegex.test(entry.ip)) {
    ElMessage.error('请输入有效的IP地址')
    return
  }
  
  if (entry.mask && !ipRegex.test(entry.mask)) {
    ElMessage.error('请输入有效的子网掩码')
    return
  }

  await updateEntry(entry)
}

const updateEntry = async (entry) => {
  try {
    await whitelistStore.updateEntry(entry.id, {
      ip: entry.ip,
      mask: entry.mask,
      description: entry.description,
      enable: entry.enable
    })
    ElMessage.success(`白名单条目 ${entry.id} 更新成功`)
  } catch (error) {
    ElMessage.error(`白名单条目 ${entry.id} 更新失败`)
  }
}

const saveGlobalConfig = async () => {
  try {
    await whitelistStore.updateGlobalConfig(globalConfig.value)
    ElMessage.success('全局配置保存成功')
    showGlobalConfigDialog.value = false
  } catch (error) {
    ElMessage.error('全局配置保存失败')
  }
}

const getNetworkSegment = (ip, mask) => {
  try {
    // 简单的网络段计算
    const ipParts = ip.split('.').map(Number)
    const maskParts = mask.split('.').map(Number)
    
    const networkParts = ipParts.map((part, index) => part & maskParts[index])
    return networkParts.join('.')
  } catch (error) {
    return '无效'
  }
}

const testEntry = (entry) => {
  ElNotification({
    title: '连接测试',
    message: `正在测试白名单条目 ${entry.id}: ${entry.ip}/${entry.mask}`,
    type: 'info',
    duration: 2000
  })
  
  // 这里可以添加实际的连接测试逻辑
  setTimeout(() => {
    ElNotification({
      title: '测试完成',
      message: `白名单条目 ${entry.id} 测试通过`,
      type: 'success',
      duration: 3000
    })
  }, 1500)
}

const testWhitelist = () => {
  ElNotification({
    title: '白名单测试',
    message: '正在测试所有启用的白名单条目...',
    type: 'info',
    duration: 2000
  })
  
  // 这里可以添加实际的白名单测试逻辑
}

// 生命周期
onMounted(() => {
  refreshWhitelist()
})
</script>

<style scoped>
.whitelist-management {
  padding: 0;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-icon {
  flex-shrink: 0;
}

.status-info {
  flex: 1;
}

.status-value {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 5px;
}

.status-label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.whitelist-table-card {
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.network-segment {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 600;
  color: #6366f1;
}

@media (max-width: 768px) {
  .status-cards .el-col {
    margin-bottom: 10px;
  }
  
  .status-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}
</style>