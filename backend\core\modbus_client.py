#!/usr/bin/env python3
"""
异步Modbus连接池管理器

实现Modbus TCP和RTU连接的连接池管理，提供连接复用、自动重连、
负载均衡等功能，支持异步并发访问。

主要功能：
1. 连接池管理 - 避免频繁创建/销毁连接
2. 异步并发 - 支持高并发Modbus读取
3. 自动重连 - 连接断开时自动重新连接
4. 健康检查 - 定期检查连接状态
5. 负载均衡 - 多个连接间的负载分配

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Any, Union, TYPE_CHECKING
from dataclasses import dataclass
from pymodbus.client import AsyncModbusTcpClient, AsyncModbusSerialClient
from pymodbus.exceptions import ModbusException
import tenacity

if TYPE_CHECKING:
    from utils.exceptions import ModbusConnectionError
else:
    try:
        from utils.exceptions import ModbusConnectionError
    except ImportError:
        # 如果相对导入失败，创建一个简单的异常类
        class ModbusConnectionError(Exception):
            pass

logger = logging.getLogger(__name__)

# 控制pymodbus库的日志级别，避免显示内部重试信息
logging.getLogger('pymodbus').setLevel(logging.ERROR)
logging.getLogger('pymodbus.client').setLevel(logging.ERROR)
logging.getLogger('pymodbus.transport').setLevel(logging.ERROR)

@dataclass
class ConnectionConfig:
    """连接配置数据类"""
    connection_type: str  # 'TCP' 或 'RTU'
    host: Optional[str] = None
    port: int = 502
    # RTU 配置
    device: Optional[str] = None
    baudrate: int = 9600
    bytesize: int = 8
    parity: str = 'N'
    stopbits: int = 1
    timeout: float = 3.0
    # 连接池配置
    max_connections: int = 10
    min_connections: int = 2
    idle_timeout: float = 300.0  # 5分钟
    retry_attempts: int = 3
    retry_delay: float = 1.0

class AsyncModbusConnection:
    """异步Modbus连接包装器"""
    
    def __init__(self, config: ConnectionConfig, connection_id: str):
        self.config = config
        self.connection_id = connection_id
        self.client = None
        self.created_at = time.time()
        self.last_used = time.time()
        self.is_healthy = False  # 初始状态应该是未连接
        self.use_count = 0
        self._lock = asyncio.Lock()
        
    async def connect(self) -> bool:
        """建立连接"""
        was_unhealthy = not self.is_healthy  # 记录之前的状态
        
        try:
            if self.config.connection_type.upper() == 'TCP':
                if self.config.host is None:
                    raise ModbusConnectionError("TCP连接需要指定host参数")
                self.client = AsyncModbusTcpClient(
                    host=self.config.host,
                    port=self.config.port,
                    timeout=self.config.timeout
                )
            else:  # RTU
                if self.config.device is None:
                    raise ModbusConnectionError("RTU连接需要指定device参数")
                self.client = AsyncModbusSerialClient(
                    port=self.config.device,
                    baudrate=self.config.baudrate,
                    bytesize=self.config.bytesize,
                    parity=self.config.parity,
                    stopbits=self.config.stopbits,
                    timeout=self.config.timeout
                )
            
            await self.client.connect()
            if self.client.connected:
                # 连接成功，检查是否为恢复场景
                if was_unhealthy and hasattr(self, '_previous_connection_failed'):
                    # 这是一个真正的恢复场景
                    logger.info(f"✓ 连接 {self.connection_id} 已恢复 - {self.config.connection_type} 连接重新建立成功")
                    delattr(self, '_previous_connection_failed')  # 清除标记
                elif not was_unhealthy:
                    # 首次成功连接
                    logger.debug(f"连接 {self.connection_id} 建立成功")
                
                self.is_healthy = True
                return True
            else:
                # 连接失败
                logger.error(f"连接 {self.connection_id} 建立失败")
                self._previous_connection_failed = True  # 标记连接失败
                if was_unhealthy:
                    logger.warning("Modbus connection lost. Attempting to reconnect...")
                self.is_healthy = False
                return False
                
        except Exception as e:
            logger.error(f"连接 {self.connection_id} 异常: {e}")
            self._previous_connection_failed = True  # 标记连接失败
            if was_unhealthy:
                logger.warning("Modbus connection lost. Attempting to reconnect...")
            self.is_healthy = False
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.client and self.client.connected:
                self.client.close()
                logger.debug(f"连接 {self.connection_id} 已断开")
        except Exception as e:
            logger.error(f"断开连接 {self.connection_id} 异常: {e}")
    
    async def is_connected(self) -> bool:
        """检查连接状态（仅检查物理连接，不包含健康状态）"""
        try:
            return self.client is not None and self.client.connected
        except:
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 记录之前的健康状态
            was_healthy = self.is_healthy
            
            # 检查当前物理连接状态
            if await self.is_connected():
                # 物理连接正常
                if not self.is_healthy:
                    # 健康状态从False变为True，这是恢复场景
                    self.is_healthy = True
                    logger.info(f"✓ 连接 {self.connection_id} 健康状态恢复")
                    logger.info("Modbus connection has been restored.")
                elif not was_healthy:
                    # 首次连接成功
                    self.is_healthy = True
                    logger.debug(f"连接 {self.connection_id} 健康检查通过")
                return True
            else:
                # 物理连接断开，尝试重新连接
                if self.is_healthy:
                    # 健康状态从True变为False，记录连接丢失
                    logger.warning(f"连接 {self.connection_id} 检测到断开，尝试重新连接")
                
                if await self.connect():
                    # 重新连接成功
                    if not was_healthy:
                        # 这是恢复场景
                        logger.info("Modbus connection has been restored.")
                    return True
                else:
                    # 重新连接失败
                    self.is_healthy = False
                    return False
            
        except Exception as e:
            logger.warning(f"连接 {self.connection_id} 健康检查异常: {e}")
            self.is_healthy = False
            return False
    
    async def read_registers(self, address: int, count: int, unit_id: int, function_code: int) -> Any:
        """带重试的寄存器读取"""
        async with self._lock:
            self.last_used = time.time()
            self.use_count += 1
            
            try:
                # 检查物理连接和健康状态
                if not (await self.is_connected() and self.is_healthy):
                    if not await self.connect():
                        self.is_healthy = False
                        raise ModbusConnectionError(f"无法连接到Modbus设备")
                
                # 确保 client 不为 None
                if self.client is None:
                    self.is_healthy = False
                    raise ModbusConnectionError(f"Modbus客户端未初始化")
                
                logger.debug(f"读取Modbus寄存器: 地址=0x{address:X}, 数量={count}, 单元ID={unit_id}, 功能码={function_code}")
                
                if function_code == 3:  # 读保持寄存器
                    result = await self.client.read_holding_registers(address, count=count)
                elif function_code == 4:  # 读输入寄存器
                    result = await self.client.read_input_registers(address, count=count)
                elif function_code == 1:  # 读线圈
                    result = await self.client.read_coils(address, count=count)
                elif function_code == 2:  # 读离散输入
                    result = await self.client.read_discrete_inputs(address, count=count)
                else:
                    self.is_healthy = False
                    raise ModbusConnectionError(f"不支持的功能码: {function_code}")
                
                if result.isError():
                    # 提供更详细的错误信息
                    error_info = self._analyze_modbus_error(result, address, unit_id, function_code)
                    logger.error(f"Modbus读取错误: {error_info}")
                    self.is_healthy = False
                    raise ModbusException(f"Modbus读取错误: {error_info}")
                
                # 从响应对象中提取实际数值
                if function_code in [3, 4]:  # 寄存器读取（返回数值）
                    if count == 1:
                        # 单个寄存器，返回单个值
                        actual_value = result.registers[0] if result.registers else 0
                    else:
                        # 多个寄存器，返回列表
                        actual_value = result.registers
                elif function_code in [1, 2]:  # 线圈和离散输入读取（返回布尔值列表）
                    if count == 1:
                        # 单个线圈/离散输入，返回布尔值
                        actual_value = result.bits[0] if result.bits else False
                    else:
                        # 多个线圈/离散输入，返回布尔值列表
                        actual_value = result.bits
                else:
                    actual_value = result
                
                logger.debug(f"Modbus读取成功: 地址=0x{address:X}, 原始结果={result}, 提取值={actual_value}")
                return actual_value
                
            except Exception as e:
                self.is_healthy = False
                error_msg = f"连接 {self.connection_id} 读取寄存器失败: {e}"
                logger.error(error_msg)
                raise
    
    def _analyze_modbus_error(self, result, address: int, unit_id: int, function_code: int) -> str:
        """分析Modbus错误并提供诊断信息"""
        try:
            if hasattr(result, 'exception_code'):
                exception_code = result.exception_code
                
                error_messages = {
                    1: "ILLEGAL FUNCTION - 不支持的功能码",
                    2: "ILLEGAL DATA ADDRESS - 非法数据地址",
                    3: "ILLEGAL DATA VALUE - 非法数据值",
                    4: "SLAVE DEVICE FAILURE - 从设备故障",
                    5: "ACKNOWLEDGE - 确认信号",
                    6: "SLAVE DEVICE BUSY - 从设备忙",
                    8: "MEMORY PARITY ERROR - 内存奇偶校验错误",
                    10: "GATEWAY PATH UNAVAILABLE - 网关路径不可用",
                    11: "GATEWAY TARGET DEVICE FAILED TO RESPOND - 网关目标设备无响应"
                }
                
                error_msg = error_messages.get(exception_code, f"未知错误码: {exception_code}")
                
                suggestion = ""
                if exception_code == 2:  # ILLEGAL DATA ADDRESS
                    suggestion = f"\n建议检查:\n" \
                               f"1. 寄存器地址 0x{address:X}({address}) 是否在设备支持范围内\n" \
                               f"2. 单元ID {unit_id} 是否正确\n" \
                               f"3. 功能码 {function_code} 是否适用于该地址\n" \
                               f"4. 设备是否在线并正常响应"
                elif exception_code == 1:  # ILLEGAL FUNCTION
                    suggestion = f"\n建议检查:\n" \
                               f"1. 功能码 {function_code} 是否被设备支持\n" \
                               f"2. 尝试使用其他功能码 (1=读线圈, 2=读离散输入, 3=读保持寄存器, 4=读输入寄存器)"
                elif exception_code == 4:  # SLAVE DEVICE FAILURE
                    suggestion = f"\n建议检查:\n" \
                               f"1. 设备是否正常运行\n" \
                               f"2. 设备是否过载或故障\n" \
                               f"3. 电源和连接是否稳定"
                
                return f"{error_msg} (地址=0x{address:X}, 单元ID={unit_id}, 功能码={function_code}){suggestion}"
            else:
                return f"未知Modbus错误: {result}"
                
        except Exception as e:
            return f"Modbus错误分析失败: {e}, 原始错误: {result}"

class AsyncModbusConnectionPool:
    """异步Modbus连接池"""
    
    def __init__(self, config: ConnectionConfig):
        self.config = config
        self.connections: Dict[str, AsyncModbusConnection] = {}
        self.available_connections = asyncio.Queue()
        self.connection_counter = 0
        self.is_running = False
        self._lock = asyncio.Lock()
        self._health_check_task = None
        
    async def start(self):
        """启动连接池"""
        if self.is_running:
            return
            
        logger.info(f"启动Modbus连接池 ({self.config.connection_type})")
        self.is_running = True
        
        # 创建最小连接数
        for _ in range(self.config.min_connections):
            await self._create_connection()
        
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
        logger.info(f"连接池启动完成，初始连接数: {len(self.connections)}")
    
    async def stop(self):
        """停止连接池"""
        if not self.is_running:
            return
            
        logger.info("停止Modbus连接池")
        self.is_running = False
        
        # 停止健康检查任务
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        for connection in self.connections.values():
            await connection.disconnect()
        
        self.connections.clear()
        
        # 清空队列
        while not self.available_connections.empty():
            try:
                self.available_connections.get_nowait()
            except asyncio.QueueEmpty:
                break
        
        logger.info("连接池已停止")
    
    async def _create_connection(self) -> Optional[AsyncModbusConnection]:
        """创建新连接"""
        async with self._lock:
            if len(self.connections) >= self.config.max_connections:
                return None
            
            self.connection_counter += 1
            connection_id = f"{self.config.connection_type}_{self.connection_counter}"
            
            connection = AsyncModbusConnection(self.config, connection_id)
            
            if await connection.connect():
                self.connections[connection_id] = connection
                await self.available_connections.put(connection)
                logger.debug(f"创建连接成功: {connection_id}")
                return connection
            else:
                logger.error(f"创建连接失败: {connection_id}")
                return None
    
    async def get_connection(self, timeout: float = 10.0) -> AsyncModbusConnection:
        """获取可用连接"""
        try:
            # 尝试从队列中获取连接
            connection = await asyncio.wait_for(
                self.available_connections.get(), 
                timeout=timeout
            )
            
            # 检查连接健康状态（物理连接+健康状态）
            if await connection.is_connected() and connection.is_healthy:
                return connection
            else:
                # 连接不健康，尝试重连
                if await connection.connect():
                    return connection
                else:
                    # 重连失败，移除连接并创建新连接
                    await self._remove_connection(connection.connection_id)
                    return await self.get_connection(timeout)
                    
        except asyncio.TimeoutError:
            # 队列为空，尝试创建新连接
            connection = await self._create_connection()
            if connection:
                return connection
            else:
                raise ModbusConnectionError("无法获取Modbus连接：连接池已满且无可用连接")
    
    async def return_connection(self, connection: AsyncModbusConnection):
        """归还连接到池中"""
        if connection.connection_id in self.connections:
            await self.available_connections.put(connection)
        else:
            # 连接已被移除，关闭它
            await connection.disconnect()
    
    async def _remove_connection(self, connection_id: str):
        """移除连接"""
        async with self._lock:
            if connection_id in self.connections:
                connection = self.connections.pop(connection_id)
                await connection.disconnect()
                logger.debug(f"移除连接: {connection_id}")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                await self._perform_health_checks()
                await self._cleanup_idle_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        unhealthy_connections = []
        
        for connection_id, connection in self.connections.items():
            if not await connection.health_check():
                unhealthy_connections.append(connection_id)
        
        # 移除不健康的连接
        for connection_id in unhealthy_connections:
            await self._remove_connection(connection_id)
        
        # 确保最小连接数
        current_count = len(self.connections)
        if current_count < self.config.min_connections:
            for _ in range(self.config.min_connections - current_count):
                await self._create_connection()
    
    async def _cleanup_idle_connections(self):
        """清理空闲连接"""
        now = time.time()
        idle_connections = []
        
        for connection_id, connection in self.connections.items():
            if (now - connection.last_used) > self.config.idle_timeout:
                if len(self.connections) > self.config.min_connections:
                    idle_connections.append(connection_id)
        
        for connection_id in idle_connections:
            await self._remove_connection(connection_id)
            logger.debug(f"清理空闲连接: {connection_id}")
    
    async def read_register(self, address: int, unit_id: int, function_code: int = 3, 
                          count: int = 1, timeout: float = 10.0) -> Any:
        """读取寄存器（高级接口）"""
        connection = await self.get_connection(timeout)
        try:
            result = await connection.read_registers(address, count, unit_id, function_code)
            return result
        except Exception as e:
            # 记录连接错误，传播异常
            logger.error(f"Modbus读取失败: {e}")
            raise
        finally:
            await self.return_connection(connection)
    
    async def check_connection_status(self) -> bool:
        """检查连接池整体连接状态"""
        if not self.is_running:
            return False
        
        # 检查是否有可用连接（物理连接+健康状态）
        healthy_connections = 0
        for connection in self.connections.values():
            if await connection.is_connected() and connection.is_healthy:
                healthy_connections += 1
        
        # 至少需要一个健康连接
        is_healthy = healthy_connections > 0
        
        if not is_healthy:
            logger.warning(f"连接池无健康连接: 健康连接数={healthy_connections}, 总连接数={len(self.connections)}")
        
        return is_healthy
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        total_connections = len(self.connections)
        available_connections = self.available_connections.qsize()
        active_connections = total_connections - available_connections
        
        return {
            'total_connections': total_connections,
            'active_connections': active_connections,
            'available_connections': available_connections,
            'max_connections': self.config.max_connections,
            'min_connections': self.config.min_connections,
            'connection_type': self.config.connection_type,
            'is_running': self.is_running
        }

