#!/bin/bash

# Web配置管理工具停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止后端服务
    if sudo supervisorctl status snmp-modbus-bridge-backend | grep -q "RUNNING"; then
        sudo supervisorctl stop snmp-modbus-bridge-backend
        log_success "后端服务已停止"
    else
        log_warning "后端服务未运行"
    fi
    
    # 可选：停止Nginx（如果不被其他服务使用）
    read -p "是否要停止Nginx服务？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        sudo systemctl stop nginx
        log_success "Nginx服务已停止"
    fi
}

# 检查服务状态
check_status() {
    log_info "检查服务状态..."
    
    # 检查后端服务
    if sudo supervisorctl status snmp-modbus-bridge-backend | grep -q "STOPPED"; then
        log_success "后端服务已停止"
    else
        log_warning "后端服务仍在运行"
    fi
    
    # 检查Nginx
    if sudo systemctl is-active --quiet nginx; then
        log_info "Nginx服务仍在运行"
    else
        log_info "Nginx服务已停止"
    fi
}

# 主函数
main() {
    echo "=========================================="
    echo "  停止 SNMP-Modbus Bridge Web配置管理工具"
    echo "=========================================="
    echo ""
    
    stop_services
    check_status
    
    log_success "服务停止完成"
}

# 运行主函数
main "$@"