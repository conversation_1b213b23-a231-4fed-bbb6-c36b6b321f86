#!/usr/bin/env python3
"""
SNMP-Modbus 桥接服务 V2 - 模块化版本

高性能、模块化的SNMP-Modbus协议桥接服务，采用现代化的模块化架构，
集成了异步处理、连接池、缓存机制、健康检查等核心功能。

主要特性：
1. 模块化架构 - 清晰的代码组织和依赖管理
2. 异步处理 - 基于asyncio的高性能异步处理
3. 连接池管理 - 高效的Modbus连接
4. 健康检查 - HTTP端点提供服务状态监控
5. SNMP扩展功能 - 支持WALK、BULK、SUBTREE等高级操作
6. 配置热重载 - 自动检测配置文件变化并热重载

SNMP扩展功能：
- SNMP GET/GETNEXT: 基本查询操作
- SNMP WALK: 遍历指定OID子树下的所有节点
- SNMP BULK: 批量获取多个OID值，提高查询效率
- SNMP SUBTREE: 获取指定OID前缀下的所有子节点

配置热重载功能：
- 自动监控config.ini文件变化
- 无需重启服务即可更新配置
- 支持OID映射、Modbus连接参数等全部配置热更新
- 智能文件变化检测（哈希值+修改时间）

作者: SNMP-Modbus Bridge Team V2
版本: 2.2.0 (模块化版本)
日期: 2025-01-09
"""

import asyncio
import signal
import sys
import os
import logging
import time
import datetime
from typing import Optional, Dict, Any, List, Tuple

# SNMP相关库
from pysnmp.carrier.asyncio.dispatch import AsyncioDispatcher
from pysnmp.carrier.asyncio.dgram import udp
from pyasn1.codec.ber import encoder, decoder
from pysnmp.proto import api

# 模块化导入
from utils import SimpleHealthChecker
from core import SimpleDataProcessor, SimpleOIDHandler
from core.modbus_client import AsyncModbusConnectionPool, ConnectionConfig
from config import SimpleConfigMonitor
from utils.config_loader import (
    get_config, get_snmp_bridge_config, get_modbus_config, get_system_oids,
    get_snmp_oids, get_modbus_type, get_timezone_config, reload_config
)

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SNMPModbusBridgeV2:
    """SNMP-Modbus桥接服务V2 - 模块化版本"""
    
    def __init__(self):
        # 获取配置
        self.config = get_config()
        
        # 创建 Modbus 连接配置
        if self.config['modbus_type'] == 'TCP':
            modbus_config = ConnectionConfig(
                connection_type='TCP',
                host=self.config['modbus_tcp']['server_ip'],
                port=self.config['modbus_tcp']['port'],
                timeout=self.config['modbus_tcp'].get('timeout', 3)
            )
        else:
            modbus_config = ConnectionConfig(
                connection_type='RTU',
                device=self.config['modbus_rtu']['port'],
                baudrate=self.config['modbus_rtu'].get('baudrate', 9600),
                timeout=self.config['modbus_rtu'].get('timeout', 3)
            )
        
        # 核心组件
        self.modbus_pool = AsyncModbusConnectionPool(modbus_config)
        self.data_processor = SimpleDataProcessor(self.config['timezone_config'])
        
        self.health_checker = SimpleHealthChecker(self.modbus_pool, None, self)
        
        # OID处理器
        self.handlers: List[SimpleOIDHandler] = []
        self.handler_index: Dict[Tuple[int, ...], SimpleOIDHandler] = {}
        
        # SNMP传输
        self.transport_dispatcher = None
        self.is_running = False
        
        # 配置监控器
        self.config_monitor = SimpleConfigMonitor()
        self.config_monitor.set_reload_callback(self._reload_config)
        
        # 重载统计
        self.reload_count = 0
        self.last_reload_time = None
        
        logger.info("✅ SNMP-Modbus桥接服务V2 (模块化版本) 初始化完成")
    
    async def _reload_config(self):
        """重载配置文件"""
        try:
            logger.info("🔄 开始重载配置...")
            
            # 重新加载配置
            old_config = self.config
            self.config = reload_config()
            
            # 重新创建OID处理器
            old_handler_count = len(self.handlers)
            self.handlers.clear()
            self.handler_index.clear()
            
            await self._create_handlers()
            
            # 如果 Modbus 连接参数有变化，重新创建连接池
            if (old_config['modbus_type'] != self.config['modbus_type'] or
                old_config['modbus_tcp'] != self.config['modbus_tcp'] or
                old_config['modbus_rtu'] != self.config['modbus_rtu']):
                
                await self.modbus_pool.stop()
                
                # 创建新的 Modbus 连接配置
                if self.config['modbus_type'] == 'TCP':
                    modbus_config = ConnectionConfig(
                        connection_type='TCP',
                        host=self.config['modbus_tcp']['server_ip'],
                        port=self.config['modbus_tcp']['port'],
                        timeout=self.config['modbus_tcp'].get('timeout', 3)
                    )
                else:
                    modbus_config = ConnectionConfig(
                        connection_type='RTU',
                        device=self.config['modbus_rtu']['port'],
                        baudrate=self.config['modbus_rtu'].get('baudrate', 9600),
                        timeout=self.config['modbus_rtu'].get('timeout', 3)
                    )
                
                self.modbus_pool = AsyncModbusConnectionPool(modbus_config)
                await self.modbus_pool.start()
                logger.info("✅ Modbus连接池已重新创建")
            
            # 更新统计信息
            self.reload_count += 1
            self.last_reload_time = time.time()
            
            logger.info(f"✅ 配置重载完成! OID处理器: {old_handler_count} -> {len(self.handlers)}")
            
            # 显示重载信息
            self._display_reload_info()
            
        except Exception as e:
            logger.error(f"❗ 配置重载失败: {e}")
            logger.error("建议检查配置文件语法是否正确")
    
    def _display_reload_info(self):
        """显示重载信息"""
        logger.info("=" * 50)
        logger.info("🔄 配置热重载信息")
        logger.info("=" * 50)
        logger.info(f"🔗 Modbus类型: {self.config['modbus_type']}")
        
        if self.config['modbus_type'] == 'TCP':
            tcp_config = self.config['modbus_tcp']
            logger.info(f"🔗 Modbus服务器: {tcp_config['server_ip']}:{tcp_config['port']}")
        else:
            rtu_config = self.config['modbus_rtu']
            logger.info(f"🔗 Modbus串口: {rtu_config['port']}")
        
        logger.info(f"📋 OID映射: {len(self.handlers)}个")
        logger.info(f"🔄 重载次数: {self.reload_count}")
        logger.info("=" * 50)
    
    async def initialize(self):
        """初始化服务"""
        logger.info("🚀 初始化SNMP-Modbus桥接服务V2 (模块化版本)")
        
        # 设置SNMP传输（优先启动SNMP服务）
        await self._setup_snmp_transport()
        
        # 创建OID处理器
        await self._create_handlers()
        
        # 最后启动Modbus连接池
        await self.modbus_pool.start()
        
        logger.info("✅ 服务初始化完成")
    
    async def _create_handlers(self):
        """创建OID处理器"""
        all_oids = list(self.config['system_oids']) + list(self.config['snmp_oids'])
        
        for oid_config in all_oids:
            try:
                handler = SimpleOIDHandler(oid_config, self.data_processor, 
                                         self.modbus_pool)
                self.handlers.append(handler)
                self.handler_index[handler.name] = handler
                logger.debug(f"创建OID处理器: {oid_config['oid']}")
            except Exception as e:
                logger.error(f"创建OID处理器失败 {oid_config.get('oid', 'unknown')}: {e}")
        
        # 按OID排序
        self.handlers.sort()
        
        logger.info(f"✓ 创建了 {len(self.handlers)} 个OID处理器")
    
    async def _setup_snmp_transport(self):
        """设置SNMP传输"""
        self.transport_dispatcher = AsyncioDispatcher()
        self.transport_dispatcher.register_recv_callback(self._snmp_callback)
        
        # 注册UDP传输
        bridge_config = self.config['snmp_bridge']
        listen_ip = bridge_config.get('listen_ip', '0.0.0.0')
        listen_port = bridge_config.get('listen_port', 1161)
        
        self.transport_dispatcher.register_transport(
            udp.DOMAIN_NAME,
            udp.UdpAsyncioTransport().open_server_mode((listen_ip, listen_port))
        )
        
        logger.info(f"✓ SNMP传输设置完成: {listen_ip}:{listen_port}")
    
    def _snmp_callback(self, transport_dispatcher, transport_domain, 
                      transport_address, whole_msg):
        """SNMP请求回调"""
        logger.debug(f"收到SNMP请求: {transport_address}")
        
        while whole_msg:
            msg_ver = api.decodeMessageVersion(whole_msg)
            if msg_ver in api.PROTOCOL_MODULES:
                p_mod = api.PROTOCOL_MODULES[msg_ver]
            else:
                logger.error(f"不支持的SNMP版本: {msg_ver}")
                return
            
            req_msg, whole_msg = decoder.decode(whole_msg, asn1Spec=p_mod.Message())
            rsp_msg = p_mod.apiMessage.get_response(req_msg)
            rsp_pdu = p_mod.apiMessage.get_pdu(rsp_msg)
            req_pdu = p_mod.apiMessage.get_pdu(req_msg)
            
            # 处理请求
            asyncio.create_task(self._process_snmp_request(
                p_mod, req_pdu, rsp_pdu, msg_ver,
                transport_dispatcher, transport_domain, transport_address, rsp_msg
            ))
        
        return whole_msg
    
    async def _process_snmp_request(self, p_mod, req_pdu, rsp_pdu, msg_ver,
                                  transport_dispatcher, transport_domain, 
                                  transport_address, rsp_msg):
        """处理SNMP请求"""
        try:
            var_binds = []
            
            # GET请求
            if req_pdu.isSameTypeWith(p_mod.GetRequestPDU()):
                for oid, val in p_mod.apiPDU.get_varbinds(req_pdu):
                    oid_tuple = tuple(oid)
                    if oid_tuple in self.handler_index:
                        handler = self.handler_index[oid_tuple]
                        value = await handler.handle_request(msg_ver)
                        var_binds.append((oid, value))
                    else:
                        # 未定义OID
                        error_value = api.PROTOCOL_MODULES[msg_ver].Integer(-99997)
                        var_binds.append((oid, error_value))
            
            # GETNEXT请求 (支持SNMP WALK)
            elif req_pdu.isSameTypeWith(p_mod.GetNextRequestPDU()):
                for oid, val in p_mod.apiPDU.get_varbinds(req_pdu):
                    oid_tuple = tuple(oid)
                    next_handler = self._find_next_handler(oid_tuple)
                    if next_handler:
                        value = await next_handler.handle_request(msg_ver)
                        var_binds.append((next_handler.name, value))
                    else:
                        # 没有更多OID，返回Null
                        var_binds.append((oid, api.PROTOCOL_MODULES[msg_ver].Null()))
            
            # GETBULK请求 (支持SNMP BULK操作)
            elif req_pdu.isSameTypeWith(p_mod.GetBulkRequestPDU()):
                var_binds = await self._handle_bulk_request(p_mod, req_pdu, msg_ver)
            
            # 设置响应
            p_mod.apiPDU.set_varbinds(rsp_pdu, var_binds)
            
            # 发送响应
            transport_dispatcher.send_message(
                encoder.encode(rsp_msg), transport_domain, transport_address
            )
        
        except Exception as e:
            logger.error(f"SNMP请求处理异常: {e}")
    
    def _find_next_handler(self, oid_tuple: Tuple[int, ...]) -> Optional[SimpleOIDHandler]:
        """查找下一个OID处理器"""
        for handler in self.handlers:
            if handler.name > oid_tuple:
                return handler
        return None
    
    async def _handle_bulk_request(self, p_mod, req_pdu, msg_ver) -> List[Tuple]:
        """处理SNMP BULK请求"""
        try:
            # 获取BULK请求参数 - 使用V1版本的方法
            non_repeaters = int(req_pdu.getComponentByName('non-repeaters'))
            max_repetitions = int(req_pdu.getComponentByName('max-repetitions'))
            
            logger.debug(f"BULK请求: non_repeaters={non_repeaters}, max_repetitions={max_repetitions}")
            
            var_binds = []
            request_oids = [tuple(oid) for oid, val in p_mod.apiPDU.get_varbinds(req_pdu)]
            
            # 处理non-repeating OIDs
            for i in range(min(non_repeaters, len(request_oids))):
                oid_tuple = request_oids[i]
                if oid_tuple in self.handler_index:
                    handler = self.handler_index[oid_tuple]
                    value = await handler.handle_request(msg_ver)
                    var_binds.append((handler.name, value))
                else:
                    # 查找下一个有效OID
                    next_handler = self._find_next_handler(oid_tuple)
                    if next_handler:
                        value = await next_handler.handle_request(msg_ver)
                        var_binds.append((next_handler.name, value))
                    else:
                        var_binds.append((oid_tuple, api.PROTOCOL_MODULES[msg_ver].Null()))
            
            # 处理repeating OIDs
            for i in range(non_repeaters, len(request_oids)):
                start_oid = request_oids[i]
                current_oid = start_oid
                
                # 最多返回max_repetitions个结果
                for rep in range(max_repetitions):
                    next_handler = self._find_next_handler(current_oid)
                    if next_handler:
                        value = await next_handler.handle_request(msg_ver)
                        var_binds.append((next_handler.name, value))
                        current_oid = next_handler.name
                    else:
                        # 没有更多OID，停止查找（不添加Null值）
                        break
            
            logger.debug(f"BULK响应: 返回{len(var_binds)}个结果")
            return var_binds
            
        except Exception as e:
            logger.error(f"BULK请求处理异常: {e}")
            return []
    
    def _find_handlers_in_subtree(self, oid_prefix: Tuple[int, ...]) -> List[SimpleOIDHandler]:
        """查找指定OID前缀下的所有处理器 (SUBTREE支持)"""
        matching_handlers = []
        
        for handler in self.handlers:
            # 检查是否以指定前缀开始
            if (len(handler.name) >= len(oid_prefix) and 
                handler.name[:len(oid_prefix)] == oid_prefix):
                matching_handlers.append(handler)
        
        return matching_handlers
    
    async def get_subtree_data(self, oid_prefix: str) -> Dict[str, Any]:
        """获取指定OID前缀下的所有数据 (SUBTREE支持)"""
        try:
            # 解析OID前缀
            if oid_prefix.startswith('.'):
                oid_prefix = oid_prefix[1:]
            oid_tuple = tuple(int(x) for x in oid_prefix.split('.'))
            
            # 查找匹配的处理器
            matching_handlers = self._find_handlers_in_subtree(oid_tuple)
            
            # 获取数据
            subtree_data = {}
            for handler in matching_handlers:
                try:
                    value = await handler.handle_request(1)  # 使用SNMP v1
                    # 将值转换为可序列化的格式
                    if hasattr(value, '_value'):
                        subtree_data[handler.oid_str] = str(value._value)
                    else:
                        subtree_data[handler.oid_str] = str(value)
                except Exception as e:
                    logger.error(f"SUBTREE数据获取失败 {handler.oid_str}: {e}")
                    subtree_data[handler.oid_str] = f"Error: {e}"
            
            return {
                'oid_prefix': oid_prefix,
                'total_nodes': len(matching_handlers),
                'data': subtree_data,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"SUBTREE操作异常: {e}")
            return {
                'oid_prefix': oid_prefix,
                'error': str(e),
                'timestamp': time.time()
            }
    
    async def start(self):
        """启动服务"""
        if self.is_running:
            return
        
        logger.info("🚀 启动SNMP-Modbus桥接服务V2 (模块化版本)")
        
        # 检查transport_dispatcher是否已初始化
        if self.transport_dispatcher is None:
            raise RuntimeError("SNMP传输未初始化，请先调用initialize()方法")
        
        self.transport_dispatcher.job_started(1)
        self.is_running = True
        
        # 启动配置监控
        asyncio.create_task(self.config_monitor.start_monitoring())
        
        # 显示启动信息
        self._display_startup_info()
        
        logger.info("✅ 服务启动完成，等待SNMP请求...")
    
    def _display_startup_info(self):
        """显示启动信息"""
        bridge_config = self.config['snmp_bridge']
        
        logger.info("=" * 60)
        logger.info("🎯 SNMP-Modbus桥接服务V2运行信息 (模块化版本)")
        logger.info("=" * 60)
        logger.info(f"📡 SNMP监听: {bridge_config.get('listen_ip', '0.0.0.0')}:{bridge_config.get('listen_port', 1161)}")
        logger.info(f"🔗 Modbus类型: {self.config['modbus_type']}")
        
        if self.config['modbus_type'] == 'TCP':
            tcp_config = self.config['modbus_tcp']
            logger.info(f"🔗 Modbus服务器: {tcp_config['server_ip']}:{tcp_config['port']}")
        else:
            rtu_config = self.config['modbus_rtu']
            logger.info(f"🔗 Modbus串口: {rtu_config['port']}")
        
        logger.info(f"📋 OID映射: {len(self.handlers)}个")
        logger.info(f"💾 缓存: 已禁用（实时模式）")
        logger.info("🚀 启动顺序: SNMP服务 → OID处理器 → Modbus连接")
        logger.info("🎆 SNMP扩展功能:")
        logger.info("  • SNMP GET/GETNEXT - 基本查询操作")
        logger.info("  • SNMP WALK - 遍历OID子树下的所有节点")
        logger.info("  • SNMP BULK - 批量获取多个OID值")
        logger.info("  • SNMP SUBTREE - 获取指定OID前缀下的所有子节点")
        logger.info("🔄 配置热重载: 启用")
        logger.info("🏗️ 模块化架构: 启用")
        logger.info("=" * 60)
    
    async def stop(self):
        """停止服务"""
        if not self.is_running:
            return
        
        logger.info("🛑 正在停止SNMP-Modbus桥接服务V2...")
        
        # 停止配置监控
        self.config_monitor.stop_monitoring()
        
        if self.transport_dispatcher:
            self.transport_dispatcher.close_dispatcher()
        
        await self.modbus_pool.stop()
        
        self.is_running = False
        logger.info("✅ 服务已停止")
    
    async def run(self):
        """运行服务"""
        try:
            await self.start()
            
            # 等待停止信号
            stop_event = asyncio.Event()
            
            def signal_handler():
                logger.info("收到停止信号")
                stop_event.set()
            
            # 注册信号处理器
            for sig in [signal.SIGINT, signal.SIGTERM]:
                try:
                    asyncio.get_event_loop().add_signal_handler(sig, signal_handler)
                except NotImplementedError:
                    # Windows不支持
                    pass
            
            await stop_event.wait()
        
        except KeyboardInterrupt:
            logger.info("收到键盘中断")
        finally:
            await self.stop()
    
    def get_reload_stats(self) -> Dict[str, Any]:
        """获取重载统计信息"""
        return {
            'reload_count': self.reload_count,
            'last_reload_time': self.last_reload_time,
            'config_monitor': self.config_monitor.get_stats(),
            'handlers_count': len(self.handlers)
        }

async def main():
    """主函数"""
    # 检查配置文件
    if not os.path.exists('config.ini'):
        print("❌ 配置文件 config.ini 不存在")
        print("请确保配置文件存在")
        sys.exit(1)
    
    # 创建服务实例
    service = SNMPModbusBridgeV2()
    
    try:
        # 初始化并运行服务
        await service.initialize()
        await service.run()
    
    except KeyboardInterrupt:
        print("\n👋 服务被用户中断")
    except Exception as e:
        print(f"❌ 服务异常: {e}")
        logger.error(f"服务异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    """程序入口点"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        sys.exit(1)