#!/usr/bin/env python3
"""
日志配置模块

提供统一的日志配置和管理功能，支持多种输出格式和日志级别。

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import logging
import logging.handlers
import os
import sys
from typing import Dict, Any
from datetime import datetime

def setup_logger(name: str = 'snmp-modbus-bridge-v2', 
                level: str = 'INFO',
                log_file: str = None,
                max_file_size: int = 10 * 1024 * 1024,  # 10MB
                backup_count: int = 5,
                console_output: bool = True,
                json_format: bool = False) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 日志文件路径
        max_file_size: 单个日志文件最大大小（字节）
        backup_count: 保留的备份文件数量
        console_output: 是否输出到控制台
        json_format: 是否使用JSON格式
    
    Returns:
        配置好的日志记录器
    """
    
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, level.upper()))
    
    # 清除已有的处理器
    logger.handlers.clear()
    
    # 创建格式化器
    if json_format:
        formatter = JsonFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    logger.info(f"日志记录器初始化完成: {name}, 级别: {level}")
    return logger

class JsonFormatter(logging.Formatter):
    """JSON格式化器"""
    
    def format(self, record):
        import json
        
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage(),
            'thread': record.thread,
            'thread_name': record.threadName,
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)

class ContextualLogger:
    """上下文日志记录器，自动添加上下文信息"""
    
    def __init__(self, logger: logging.Logger, context: Dict[str, Any] = None):
        self.logger = logger
        self.context = context or {}
    
    def _log(self, level: int, message: str, **kwargs):
        """内部日志方法"""
        extra_fields = dict(self.context)
        extra_fields.update(kwargs)
        
        # 创建LogRecord并添加额外字段
        record = self.logger.makeRecord(
            self.logger.name, level, '', 0, message, (), None
        )
        record.extra_fields = extra_fields
        
        self.logger.handle(record)
    
    def debug(self, message: str, **kwargs):
        self._log(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        self._log(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        self._log(logging.CRITICAL, message, **kwargs)
    
    def add_context(self, **kwargs):
        """添加上下文信息"""
        self.context.update(kwargs)
    
    def remove_context(self, *keys):
        """移除上下文信息"""
        for key in keys:
            self.context.pop(key, None)

def get_logger(name: str = None, **kwargs) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    if name is None:
        name = 'snmp-modbus-bridge-v2'
    
    # 如果已经配置过，直接返回
    logger = logging.getLogger(name)
    if logger.handlers:
        return logger
    
    # 否则进行配置
    return setup_logger(name, **kwargs)