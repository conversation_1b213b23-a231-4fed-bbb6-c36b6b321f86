#!/usr/bin/env python3
"""
数据处理器

负责Modbus数据类型转换、数值处理和SNMP格式转换。

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import logging
import datetime
from typing import Any, Optional, Dict
from pysnmp.proto import api

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器"""
    
    def __init__(self, timezone_offset: str = '+08'):
        self.timezone_offset = timezone_offset
        logger.info(f"初始化数据处理器，时区: {timezone_offset}")
    
    async def process_modbus_data(self, raw_value: Any, mapping) -> Optional[Any]:
        """处理Modbus数据"""
        try:
            # 数据类型转换
            converted_value = await self._convert_data_type(raw_value, mapping.data_type)
            
            # 数据处理
            if mapping.processing_type == 'multiply':
                processed_value = converted_value * mapping.coefficient + mapping.offset
                if mapping.decimal_places > 0:
                    processed_value = round(processed_value, mapping.decimal_places)
            elif mapping.processing_type == 'direct':
                processed_value = converted_value
            else:
                processed_value = converted_value
            
            logger.debug(f"数据处理: {raw_value} -> {converted_value} -> {processed_value}")
            return processed_value
            
        except Exception as e:
            logger.error(f"数据处理异常: {e}")
            return None
    
    async def _convert_data_type(self, raw_value: Any, data_type: str) -> Any:
        """转换数据类型"""
        try:
            if data_type == 'int16':
                # 有符号16位整数
                if raw_value > 32767:
                    return raw_value - 65536
                return raw_value
            elif data_type == 'uint16':
                # 无符号16位整数
                return raw_value
            elif data_type == 'int32':
                # 有符号32位整数
                if raw_value > 2147483647:
                    return raw_value - 4294967296
                return raw_value
            elif data_type == 'uint32':
                # 无符号32位整数
                return raw_value
            elif data_type == 'float32':
                # 32位浮点数
                return float(raw_value)
            else:
                return raw_value
                
        except Exception as e:
            logger.error(f"数据类型转换异常: {e}")
            return raw_value
    
    async def convert_to_snmp_value(self, value: Any, proto_ver: int, 
                                  snmp_data_type: str = 'OctetString') -> Any:
        """转换为SNMP值"""
        try:
            if snmp_data_type == 'Integer':
                return api.PROTOCOL_MODULES[proto_ver].Integer(int(value))
            elif snmp_data_type == 'OctetString':
                return api.PROTOCOL_MODULES[proto_ver].OctetString(str(value))
            elif snmp_data_type == 'Gauge':
                return api.PROTOCOL_MODULES[proto_ver].Gauge(int(value))
            elif snmp_data_type == 'Counter':
                return api.PROTOCOL_MODULES[proto_ver].Counter(int(value))
            elif snmp_data_type == 'TimeTicks':
                return api.PROTOCOL_MODULES[proto_ver].TimeTicks(int(value))
            else:
                return api.PROTOCOL_MODULES[proto_ver].OctetString(str(value))
                
        except Exception as e:
            logger.error(f"SNMP值转换异常: {e}")
            return api.PROTOCOL_MODULES[proto_ver].OctetString("Error")
    
    async def get_utc_time(self) -> str:
        """获取UTC时间字符串"""
        try:
            # 解析时区偏移
            if self.timezone_offset.startswith('+') or self.timezone_offset.startswith('-'):
                sign = 1 if self.timezone_offset[0] == '+' else -1
                hours = int(self.timezone_offset[1:3])
                offset_minutes = sign * hours * 60
            else:
                offset_minutes = 0
            
            # 创建时区对象
            target_timezone = datetime.timezone(datetime.timedelta(minutes=offset_minutes))
            
            # 获取当前时间并转换到目标时区
            utc_now = datetime.datetime.now(datetime.timezone.utc)
            local_time = utc_now.astimezone(target_timezone)
            
            # 生成时间字符串：YYYYMMDDTHHMMSS+时区偏移
            time_str = local_time.strftime('%Y%m%dT%H%M%S')
            return f"{time_str}{self.timezone_offset}"
            
        except Exception as e:
            logger.error(f"UTC时间获取异常: {e}")
            return datetime.datetime.now().strftime('%Y%m%dT%H%M%S+00')

# 为兼容性添加简化的数据处理器
class SimpleDataProcessor:
    """简化的数据处理器（兼容现有接口）"""
    
    def __init__(self, timezone_config: Optional[Dict] = None):
        self.timezone_offset = timezone_config.get('timezone_offset', '+08') if timezone_config else '+08'
        logger.info(f"初始化简化数据处理器，时区: {self.timezone_offset}")
    
    async def process_modbus_data(self, raw_value: Any, mapping_or_config) -> Optional[Any]:
        """处理Modbus数据（兼容旧版接口）
        
        参数:
        - raw_value: 原始数据值
        - mapping_or_config: OIDMapping对象或字典配置
        """
        try:
            # 首先检查并处理 Modbus 响应对象
            actual_value = self._extract_actual_value(raw_value)
            
            # 检查参数类型，支持OIDMapping对象和字典
            if hasattr(mapping_or_config, 'data_type'):  # OIDMapping对象
                mapping = mapping_or_config
                data_type = mapping.data_type
                processing_type = mapping.processing_type
                coefficient = getattr(mapping, 'coefficient', 1.0)
                offset = getattr(mapping, 'offset', 0.0)
                decimal_places = getattr(mapping, 'decimal_places', 0)
            else:  # 字典配置
                oid_config = mapping_or_config
                data_type = oid_config.get('data_type', 'uint16')
                processing_type = oid_config['data_processing']['type']
                coefficient = oid_config['data_processing'].get('coefficient', 1.0)
                offset = oid_config['data_processing'].get('offset', 0.0)
                decimal_places = oid_config['data_processing'].get('decimal_places', 0)
            
            # 确保 actual_value 是数值类型
            if not isinstance(actual_value, (int, float)):
                logger.warning(f"非数值类型的数据: {type(actual_value)} = {actual_value}, 尝试转换")
                try:
                    actual_value = float(actual_value)
                except (ValueError, TypeError):
                    logger.error(f"无法将数据转换为数值: {actual_value}")
                    return None
            
            # 数据类型转换
            if data_type == 'int16' and actual_value > 32767:
                converted_value = actual_value - 65536
            else:
                converted_value = actual_value
            
            # 数据处理
            if processing_type == 'multiply':
                processed_value = converted_value * coefficient + offset
                if decimal_places > 0:
                    processed_value = round(processed_value, decimal_places)
            else:
                processed_value = converted_value
            
            logger.debug(f"数据处理: {raw_value} -> {actual_value} -> {converted_value} -> {processed_value}")
            return processed_value
        
        except Exception as e:
            logger.error(f"数据处理异常: {e}")
            return None
    
    def _extract_actual_value(self, raw_value: Any) -> Any:
        """从原始值中提取实际数值，处理 Modbus 响应对象"""
        try:
            # 如果是基本数值类型，直接返回
            if isinstance(raw_value, (int, float, bool)):
                return raw_value
            
            # 如果是列表，返回第一个元素（单个寄存器的情况）
            if isinstance(raw_value, list) and len(raw_value) > 0:
                return raw_value[0]
            
            # 尝试检查是否是 Modbus 响应对象（排除列表类型）
            if not isinstance(raw_value, list):
                if hasattr(raw_value, 'registers') and getattr(raw_value, 'registers', None):
                    # 寄存器读取响应
                    registers = raw_value.registers
                    return registers[0] if len(registers) == 1 else registers
                elif hasattr(raw_value, 'bits') and getattr(raw_value, 'bits', None):
                    # 线圈/离散输入读取响应
                    bits = raw_value.bits
                    return bits[0] if len(bits) == 1 else bits
            
            # 其他情况，尝试直接转换
            return raw_value
            
        except Exception as e:
            logger.warning(f"提取实际数值失败: {e}, 原始值: {raw_value}")
            return raw_value
    
    async def convert_to_snmp_value(self, value: Any, proto_ver: int, 
                                  snmp_data_type: str = 'OctetString') -> Any:
        """转换为SNMP值"""
        try:
            # 需要导入api
            from pysnmp.proto import api
            
            if snmp_data_type == 'Integer':
                return api.PROTOCOL_MODULES[proto_ver].Integer(int(value))
            elif snmp_data_type == 'OctetString':
                return api.PROTOCOL_MODULES[proto_ver].OctetString(str(value).encode())
            else:
                return api.PROTOCOL_MODULES[proto_ver].OctetString(str(value).encode())
        except Exception as e:
            logger.error(f"SNMP值转换异常: {e}")
            try:
                from pysnmp.proto import api
                return api.PROTOCOL_MODULES[proto_ver].OctetString(b"Error")
            except:
                return "Error"
    
    async def get_utc_time(self) -> str:
        """获取UTC时间字符串"""
        try:
            # 解析时区偏移
            if self.timezone_offset.startswith(('+', '-')):
                sign = 1 if self.timezone_offset[0] == '+' else -1
                hours = int(self.timezone_offset[1:3])
                offset_minutes = sign * hours * 60
            else:
                offset_minutes = 0
            
            # 创建时区对象
            target_timezone = datetime.timezone(datetime.timedelta(minutes=offset_minutes))
            
            # 获取当前时间并转换到目标时区
            utc_now = datetime.datetime.now(datetime.timezone.utc)
            local_time = utc_now.astimezone(target_timezone)
            
            # 生成时间字符串
            time_str = local_time.strftime('%Y%m%dT%H%M%S')
            return f"{time_str}{self.timezone_offset}"
        
        except Exception as e:
            logger.error(f"UTC时间获取异常: {e}")
            return datetime.datetime.now().strftime('%Y%m%dT%H%M%S+00')