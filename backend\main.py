# -*- coding: utf-8 -*-
"""
SNMP-Modbus Bridge Web配置管理工具 - 后端API服务
基于FastAPI实现，提供配置管理和网络接口配置功能

Author: AI Assistant
Version: 2.0.0
Target: Debian/Ubuntu

New Features in v2.0.0:
- NMS白名单管理
- UDP并发支持配置
- 日志查看功能
- 简单登录认证
"""

import os
import sys
import json
import subprocess
import configparser
import hashlib
import secrets
import ipaddress
import asyncio
import threading
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from fastapi import FastAPI, HTTPException, UploadFile, File, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field, field_validator
import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入项目配置加载器
from utils.config_loader import ConfigLoader

app = FastAPI(
    title="SNMP-Modbus Bridge 配置管理API",
    description="Web配置管理工具后端API，支持config.ini管理、Linux网络接口配置、NMS白名单管理、日志查看和登录认证",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 认证配置
security = HTTPBearer()

# 配置文件路径
CONFIG_FILE_PATH = Path(__file__).parent / "config.ini"
BACKUP_DIR = project_root / "config_backups"
LOG_DIR = Path("/var/log/snmp-modbus-bridge")
SNMP_BRIDGE_SCRIPT = Path(__file__).parent / "snmp-modbus-bridgev2.py"
STATIC_DIR = project_root / "web-config-manager" / "frontend" / "dist"

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 静态文件服务配置（生产环境）
if STATIC_DIR.exists():
    # 挂载静态文件目录
    app.mount("/static", StaticFiles(directory=str(STATIC_DIR / "static")), name="static")
    
    # 添加默认路由处理SPA
    @app.get("/")
    async def serve_spa():
        """SPA应用主页面"""
        index_file = STATIC_DIR / "index.html"
        if index_file.exists():
            return FileResponse(str(index_file))
        else:
            return {"message": "前端静态文件未构建，请运行 npm run build"}
    
    # 处理所有非-API路由，返回index.html支持Vue Router
    @app.get("/{path:path}")
    async def serve_spa_routes(path: str):
        """SPA路由支持"""
        # 如果是API路由，跳过
        if path.startswith("api/") or path.startswith("docs") or path.startswith("redoc"):
            raise HTTPException(status_code=404, detail="API路由不存在")
        
        # 检查是否是静态资源文件
        file_path = STATIC_DIR / path
        if file_path.exists() and file_path.is_file():
            return FileResponse(str(file_path))
        
        # 否则返回index.html
        index_file = STATIC_DIR / "index.html"
        if index_file.exists():
            return FileResponse(str(index_file))
        else:
            return {"message": "前端静态文件未构建，请运行 npm run build"}
else:
    @app.get("/")
    async def development_info():
        """SNMP-Modbus Bridge Web配置管理工具 - 开发模式"""
        return {
            "service": "SNMP-Modbus Bridge Web配置管理工具",
            "mode": "development",
            "version": "2.0.0",
            "status": "running",
            "timestamp": datetime.now().isoformat(),
            "frontend": "http://localhost:3000",
            "backend": "http://localhost:8000",
            "docs": "http://localhost:8000/docs",
            "note": "开发模式下需要分别启动前后端服务",
            "features": [
                "config_management",
                "network_configuration",
                "nms_whitelist",
                "log_viewer",
                "authentication",
                "udp_concurrent_support",
                "snmp_bridge_service_management"
            ]
        }

# 会话存储（简单实现，生产环境应使用Redis等）
active_sessions = {}

# SNMP桥接服务状态
bridge_service_process = None
bridge_service_status = {
    'running': False,
    'pid': None,
    'start_time': None,
    'restart_count': 0
}

# 新增数据模型
class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")

class LoginResponse(BaseModel):
    """登录响应模型"""
    success: bool = Field(..., description="登录是否成功")
    token: Optional[str] = Field(None, description="访问令牌")
    expires_at: Optional[str] = Field(None, description="令牌过期时间")
    message: str = Field(..., description="响应消息")

class WhitelistEntry(BaseModel):
    """白名单条目模型"""
    ip: str = Field(..., description="IP地址")
    mask: str = Field(..., description="子网掩码")
    description: str = Field("", description="描述信息")
    enable: bool = Field(True, description="是否启用")
    
    @field_validator('ip')
    @classmethod
    def validate_ip(cls, v):
        try:
            ipaddress.IPv4Address(v)
            return v
        except ipaddress.AddressValueError:
            raise ValueError('非法的IP地址格式')
    
    @field_validator('mask')
    @classmethod
    def validate_mask(cls, v):
        try:
            ipaddress.IPv4Address(v)
            return v
        except ipaddress.AddressValueError:
            raise ValueError('非法的子网掩码格式')

class LogQuery(BaseModel):
    """日志查询模型"""
    lines: int = Field(100, description="要读取的行数", ge=1, le=10000)
    level: Optional[str] = Field(None, description="日志级别过滤")
    search: Optional[str] = Field(None, description="搜索关键字")
    start_time: Optional[str] = Field(None, description="开始时间")
    end_time: Optional[str] = Field(None, description="结束时间")

class ServiceControl(BaseModel):
    """服务控制模型"""
    action: str = Field(..., description="操作类型: start/stop/restart")
    
class ServiceStatus(BaseModel):
    """服务状态模型"""
    running: bool = Field(..., description="是否运行中")
    pid: Optional[int] = Field(None, description="进程id")
    start_time: Optional[str] = Field(None, description="启动时间")
    restart_count: int = Field(0, description="重启次数")

# 数据模型定义
class ConfigSection(BaseModel):
    """配置节模型"""
    name: str = Field(..., description="配置节名称")
    items: Dict[str, str] = Field(..., description="配置项键值对")

class ConfigUpdate(BaseModel):
    """配置更新模型"""
    section: str = Field(..., description="配置节名称")
    key: str = Field(..., description="配置键")
    value: str = Field(..., description="配置值")

class NetworkInterface(BaseModel):
    """网络接口模型"""
    name: str = Field(..., description="接口名称")
    ip: Optional[str] = Field(None, description="IP地址")
    netmask: Optional[str] = Field(None, description="子网掩码")
    gateway: Optional[str] = Field(None, description="网关")
    dns: Optional[List[str]] = Field(None, description="DNS服务器列表")
    dhcp: bool = Field(False, description="是否使用DHCP")
    status: str = Field(..., description="接口状态")

class NetworkConfig(BaseModel):
    """网络配置模型"""
    interface: str = Field(..., description="网络接口名称")
    ip: Optional[str] = Field(None, description="IP地址")
    netmask: Optional[str] = Field("*************", description="子网掩码")
    gateway: Optional[str] = Field(None, description="网关")
    dns: Optional[List[str]] = Field(None, description="DNS服务器列表")
    dhcp: bool = Field(False, description="是否使用DHCP")

# 工具函数
def create_backup_dir():
    """创建备份目录"""
    BACKUP_DIR.mkdir(exist_ok=True)

def backup_config():
    """备份当前配置文件"""
    create_backup_dir()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_file = BACKUP_DIR / f"config_backup_{timestamp}.ini"
    
    if CONFIG_FILE_PATH.exists():
        import shutil
        shutil.copy2(CONFIG_FILE_PATH, backup_file)
        return str(backup_file)
    return None

def load_config():
    """加载配置文件"""
    if not CONFIG_FILE_PATH.exists():
        raise HTTPException(status_code=404, detail="配置文件不存在")
    
    try:
        config_loader = ConfigLoader(str(CONFIG_FILE_PATH))
        return config_loader
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置文件加载失败: {str(e)}")

def run_system_command(command: List[str]) -> Dict[str, Any]:
    """执行系统命令"""
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            timeout=30
        )
        return {
            "success": result.returncode == 0,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode
        }
    except subprocess.TimeoutExpired:
        return {
            "success": False,
            "stdout": "",
            "stderr": "命令执行超时",
            "returncode": -1
        }
    except Exception as e:
        return {
            "success": False,
            "stdout": "",
            "stderr": str(e),
            "returncode": -1
        }

# 认证相关函数
def hash_password(password: str) -> str:
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return hash_password(password) == hashed

def generate_token() -> str:
    """生成访问令牌"""
    return secrets.token_urlsafe(32)

def get_auth_config():
    """获取认证配置"""
    try:
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        if 'WEB_AUTH' not in config.sections():
            return {
                'enable': True,
                'default_username': 'admin',
                'default_password': 'admin1234',
                'session_timeout': 60
            }
        
        auth_section = config['WEB_AUTH']
        return {
            'enable': auth_section.getboolean('enable', True),
            'default_username': auth_section.get('default_username', 'admin'),
            'default_password': auth_section.get('default_password', 'admin123'),
            'session_timeout': auth_section.getint('session_timeout', 60)
        }
    except Exception:
        return {
            'enable': True,
            'default_username': 'admin',
            'default_password': 'admin1234',
            'session_timeout': 60
        }

def verify_token(token: str) -> bool:
    """验证令牌有效性"""
    if token not in active_sessions:
        return False
    
    session = active_sessions[token]
    if datetime.now() > session['expires_at']:
        del active_sessions[token]
        return False
    
    return True

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户（认证依赖）"""
    auth_config = get_auth_config()
    
    # 如果禁用认证，直接通过
    if not auth_config['enable']:
        return {'username': 'anonymous'}
    
    token = credentials.credentials
    if not verify_token(token):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌无效或已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return active_sessions[token]['user']

# 白名单管理函数
def get_whitelist_config():
    """获取白名单配置"""
    try:
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        whitelist_config = {
            'enable': True,
            'default_policy': 'deny',
            'entries': []
        }
        
        if 'NMS_WHITELIST' in config.sections():
            nms_section = config['NMS_WHITELIST']
            whitelist_config['enable'] = nms_section.getboolean('enable', True)
            whitelist_config['default_policy'] = nms_section.get('default_policy', 'deny')
        
        # 获取白名单条目
        for i in range(1, 11):  # 支持有10个白名单条目
            section_name = f"NMS_WHITELIST_{i}"
            if section_name in config.sections():
                entry_section = config[section_name]
                entry = {
                    'id': i,
                    'ip': entry_section.get('ip', ''),
                    'mask': entry_section.get('mask', ''),
                    'description': entry_section.get('description', ''),
                    'enable': entry_section.getboolean('enable', True)
                }
                whitelist_config['entries'].append(entry)
        
        return whitelist_config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取白名单配置失败: {str(e)}")

# 日志管理函数
def get_log_files():
    """获取日志文件列表"""
    log_files = []
    
    # 默认日志路径
    default_paths = [
        LOG_DIR / "service.log",
        LOG_DIR / "backend.log",
        LOG_DIR / "snmp-modbus-bridge.log",
        Path("/var/log/snmp-modbus-bridge.log"),
        project_root / "snmp_modbus_bridge.log"
    ]
    
    for log_path in default_paths:
        if log_path.exists() and log_path.is_file():
            stat = log_path.stat()
            log_files.append({
                'name': log_path.name,
                'path': str(log_path),
                'size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
            })
    
    return log_files

def read_log_file(file_path: str, lines: int = 100, search: Optional[str] = None):
    """读取日志文件"""
    try:
        log_path = Path(file_path)
        if not log_path.exists():
            raise HTTPException(status_code=404, detail="日志文件不存在")
        
        # 使用tail命令读取最后几行
        cmd = ["tail", "-n", str(lines), str(log_path)]
        result = run_system_command(cmd)
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"读取日志失败: {result['stderr']}")
        
        log_lines = result["stdout"].split("\n")
        
        # 如果有搜索关键字，进行过滤
        if search:
            log_lines = [line for line in log_lines if search.lower() in line.lower()]
        
        return {
            'lines': log_lines,
            'total': len(log_lines),
            'file_path': file_path
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取日志失败: {str(e)}")

# SNMP桥接服务管理函数
def get_bridge_service_status():
    """获取SNMP桥接服务状态"""
    global bridge_service_process, bridge_service_status
    
    # 检查进程是否还在运行
    if bridge_service_process and bridge_service_process.poll() is None:
        bridge_service_status['running'] = True
        bridge_service_status['pid'] = bridge_service_process.pid
    else:
        bridge_service_status['running'] = False
        bridge_service_status['pid'] = None
        bridge_service_process = None
    
    return bridge_service_status.copy()

def start_bridge_service():
    """启动SNMP桥接服务"""
    global bridge_service_process, bridge_service_status
    
    try:
        # 检查脚本是否存在
        if not SNMP_BRIDGE_SCRIPT.exists():
            raise Exception(f"SNMP桥接服务脚本不存在: {SNMP_BRIDGE_SCRIPT}")
        
        # 如果已经在运行，先停止
        if bridge_service_process and bridge_service_process.poll() is None:
            stop_bridge_service()
        
        # 启动新进程
        bridge_service_process = subprocess.Popen(
            [sys.executable, str(SNMP_BRIDGE_SCRIPT)],
            cwd=str(project_root),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 更新状态
        bridge_service_status.update({
            'running': True,
            'pid': bridge_service_process.pid,
            'start_time': datetime.now().isoformat(),
            'restart_count': bridge_service_status.get('restart_count', 0) + 1
        })
        
        return {
            'success': True,
            'pid': bridge_service_process.pid,
            'message': 'SNMP桥接服务启动成功'
        }
    except Exception as e:
        bridge_service_status['running'] = False
        bridge_service_status['pid'] = None
        return {
            'success': False,
            'message': f'SNMP桥接服务启动失败: {str(e)}'
        }

def stop_bridge_service():
    """停止SNMP桥接服务"""
    global bridge_service_process, bridge_service_status
    
    try:
        if bridge_service_process and bridge_service_process.poll() is None:
            bridge_service_process.terminate()
            # 等待进程结束
            bridge_service_process.wait(timeout=10)
        
        bridge_service_status.update({
            'running': False,
            'pid': None
        })
        
        bridge_service_process = None
        
        return {
            'success': True,
            'message': 'SNMP桥接服务停止成功'
        }
    except subprocess.TimeoutExpired:
        # 强制终止
        if bridge_service_process:
            bridge_service_process.kill()
            bridge_service_process = None
        
        bridge_service_status.update({
            'running': False,
            'pid': None
        })
        
        return {
            'success': True,
            'message': 'SNMP桥接服务强制停止成功'
        }
    except Exception as e:
        return {
            'success': False,
            'message': f'SNMP桥接服务停止失败: {str(e)}'
        }

def restart_bridge_service():
    """重启SNMP桥接服务"""
    # 先停止
    stop_result = stop_bridge_service()
    if not stop_result['success']:
        return stop_result
    
    # 稍等一下
    import time
    time.sleep(2)
    
    # 再启动
    return start_bridge_service()

# API路由

@app.get("/api", summary="API根路径")
async def api_root():
    """API根路径，返回服务信息"""
    return {
        "service": "SNMP-Modbus Bridge Web配置管理工具",
        "version": "2.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "config_management",
            "network_configuration", 
            "nms_whitelist",
            "log_viewer",
            "authentication",
            "udp_concurrent_support",
            "snmp_bridge_service_management"
        ]
    }

@app.get("/health", summary="健康检查")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "config_file_exists": CONFIG_FILE_PATH.exists(),
        "version": "2.0.0"
    }

# 认证API
@app.post("/api/auth/login", summary="用户登录", response_model=LoginResponse)
async def login(login_request: LoginRequest):
    """用户登录接口"""
    try:
        auth_config = get_auth_config()
        
        # 如果禁用认证，直接返回成功
        if not auth_config['enable']:
            return LoginResponse(
                success=True,
                token="no-auth-required",
                expires_at=(datetime.now() + timedelta(hours=24)).isoformat(),
                message="认证已禁用，直接登录成功"
            )
        
        # 验证用户名和密码
        if (login_request.username != auth_config['default_username'] or 
            login_request.password != auth_config['default_password']):
            return LoginResponse(
                success=False,
                token=None,
                expires_at=None,
                message="用户名或密码错误"
            )
        
        # 生成令牌
        token = generate_token()
        expires_at = datetime.now() + timedelta(minutes=auth_config['session_timeout'])
        
        # 存储会话
        active_sessions[token] = {
            'user': {
                'username': login_request.username,
                'login_time': datetime.now().isoformat()
            },
            'expires_at': expires_at
        }
        
        return LoginResponse(
            success=True,
            token=token,
            expires_at=expires_at.isoformat(),
            message="登录成功"
        )
    except Exception as e:
        return LoginResponse(
            success=False,
            token=None,
            expires_at=None,
            message=f"登录失败: {str(e)}"
        )

@app.post("/api/auth/logout", summary="用户登出")
async def logout(current_user = Depends(get_current_user), credentials: HTTPAuthorizationCredentials = Depends(security)):
    """用户登出接口"""
    try:
        token = credentials.credentials
        if token in active_sessions:
            del active_sessions[token]
        
        return {
            "success": True,
            "message": "登出成功"
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"登出失败: {str(e)}"
        }

@app.get("/api/auth/user", summary="获取当前用户信息")
async def get_user_info(current_user = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "success": True,
        "data": current_user,
        "message": "用户信息获取成功"
    }

# NMS白名单API
@app.get("/api/whitelist", summary="获取NMS白名单配置")
async def get_whitelist(current_user = Depends(get_current_user)):
    """获取NMS白名单配置"""
    try:
        whitelist_config = get_whitelist_config()
        return {
            "success": True,
            "data": whitelist_config,
            "message": "NMS白名单获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取NMS白名单失败: {str(e)}")

@app.put("/api/whitelist/{entry_id}", summary="更新白名单条目")
async def update_whitelist_entry(entry_id: int, entry: WhitelistEntry, current_user = Depends(get_current_user)):
    """更新白名单条目"""
    try:
        if entry_id < 1 or entry_id > 10:
            raise HTTPException(status_code=400, detail="白名单条目ID必须在1-10之间")
        
        # 备份配置
        backup_file = backup_config()
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        section_name = f"NMS_WHITELIST_{entry_id}"
        
        # 如果配置节不存在，创建它
        if section_name not in config.sections():
            config.add_section(section_name)
        
        # 更新配置
        config[section_name]['ip'] = entry.ip
        config[section_name]['mask'] = entry.mask
        config[section_name]['description'] = entry.description
        config[section_name]['enable'] = str(entry.enable).lower()
        
        # 保存配置
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return {
            "success": True,
            "data": {
                "entry_id": entry_id,
                "entry": entry.dict(),
                "backup_file": backup_file
            },
            "message": f"白名单条目 {entry_id} 更新成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"白名单条目更新失败: {str(e)}")

@app.put("/api/whitelist/config", summary="更新白名单全局配置")
async def update_whitelist_config(config_data: Dict[str, Any], current_user = Depends(get_current_user)):
    """更新白名单全局配置"""
    try:
        # 备份配置
        backup_file = backup_config()
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        # 确保白名单配置节存在
        if 'NMS_WHITELIST' not in config.sections():
            config.add_section('NMS_WHITELIST')
        
        # 更新全局配置
        if 'enable' in config_data:
            config['NMS_WHITELIST']['enable'] = str(config_data['enable']).lower()
        if 'default_policy' in config_data:
            config['NMS_WHITELIST']['default_policy'] = config_data['default_policy']
        
        # 保存配置
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return {
            "success": True,
            "data": {
                "config": config_data,
                "backup_file": backup_file
            },
            "message": "白名单全局配置更新成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"白名单配置更新失败: {str(e)}")

# SNMP桥接服务管理API
@app.get("/api/bridge/status", summary="获取SNMP桥接服务状态")
async def get_bridge_status(current_user = Depends(get_current_user)):
    """获取SNMP桥接服务状态"""
    try:
        status = get_bridge_service_status()
        return {
            "success": True,
            "data": status,
            "message": "SNMP桥接服务状态获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")

@app.post("/api/bridge/control", summary="控制SNMP桥接服务")
async def control_bridge_service(control: ServiceControl, current_user = Depends(get_current_user)):
    """控制SNMP桥接服务（启动/停止/重启）"""
    try:
        if control.action == "start":
            result = start_bridge_service()
        elif control.action == "stop":
            result = stop_bridge_service()
        elif control.action == "restart":
            result = restart_bridge_service()
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {control.action}")
        
        return {
            "success": result['success'],
            "data": {
                "action": control.action,
                "result": result
            },
            "message": result['message']
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务控制失败: {str(e)}")

@app.get("/api/bridge/logs", summary="获取SNMP桥接服务日志")
async def get_bridge_logs(lines: int = 100, search: Optional[str] = None, current_user = Depends(get_current_user)):
    """获取SNMP桥接服务日志"""
    try:
        # 查找相关日志文件
        log_files = get_log_files()
        bridge_logs = []
        
        # 查找包含snmp或bridge关键字的日志文件
        for log_file in log_files:
            file_name_lower = log_file['name'].lower()
            if 'snmp' in file_name_lower or 'bridge' in file_name_lower:
                try:
                    log_data = read_log_file(log_file['path'], lines, search)
                    bridge_logs.append({
                        'file_name': log_file['name'],
                        'file_path': log_file['path'],
                        'logs': log_data['lines'][:lines]  # 限制行数
                    })
                except Exception as e:
                    logger.warning(f"读取日志文件 {log_file['name']} 失败: {e}")
        
        # 如果没找到专用日志，尝试从当前进程获取输出
        if not bridge_logs and bridge_service_process:
            try:
                # 这里可以添加从进程获取日志的逻辑
                bridge_logs.append({
                    'file_name': 'runtime_output',
                    'file_path': 'process_output',
                    'logs': ['服务正在运行中，日志输出可能在系统日志中']
                })
            except Exception:
                pass
        
        return {
            "success": True,
            "data": {
                "logs": bridge_logs,
                "total_files": len(bridge_logs),
                "lines_per_file": lines,
                "search_keyword": search
            },
            "message": "SNMP桥接服务日志获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取桥接服务日志失败: {str(e)}")

@app.get("/api/logs/{file_name}", summary="读取日志文件内容")
async def read_log(file_name: str, lines: int = 100, search: Optional[str] = None, current_user = Depends(get_current_user)):
    """读取日志文件内容"""
    try:
        # 查找日志文件
        log_files = get_log_files()
        target_file = None
        
        for log_file in log_files:
            if log_file['name'] == file_name:
                target_file = log_file['path']
                break
        
        if not target_file:
            raise HTTPException(status_code=404, detail=f"日志文件 {file_name} 不存在")
        
        log_data = read_log_file(target_file, lines, search)
        
        return {
            "success": True,
            "data": log_data,
            "message": f"日志文件 {file_name} 读取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取日志文件失败: {str(e)}")

@app.get("/api/logs/download/{file_name}", summary="下载日志文件")
async def download_log(file_name: str, current_user = Depends(get_current_user)):
    """下载日志文件"""
    try:
        # 查找日志文件
        log_files = get_log_files()
        target_file = None
        
        for log_file in log_files:
            if log_file['name'] == file_name:
                target_file = log_file['path']
                break
        
        if not target_file or not Path(target_file).exists():
            raise HTTPException(status_code=404, detail=f"日志文件 {file_name} 不存在")
        
        return FileResponse(
            path=target_file,
            filename=file_name,
            media_type="application/octet-stream"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载日志文件失败: {str(e)}")

# 配置管理API
@app.get("/api/config", summary="获取完整配置")
async def get_config(current_user = Depends(get_current_user)):
    """获取完整的配置文件内容"""
    try:
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        result = {}
        for section_name in config.sections():
            result[section_name] = dict(config[section_name])
        
        return {
            "success": True,
            "data": result,
            "message": "配置获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置获取失败: {str(e)}")

@app.get("/api/config/{section}", summary="获取指定配置节")
async def get_config_section(section: str):
    """获取指定配置节的内容"""
    try:
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        if section not in config.sections():
            raise HTTPException(status_code=404, detail=f"配置节 {section} 不存在")
        
        return {
            "success": True,
            "data": {
                "section": section,
                "items": dict(config[section])
            },
            "message": f"配置节 {section} 获取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置获取失败: {str(e)}")

@app.put("/api/config/{section}/{key}", summary="更新配置项")
async def update_config_item(section: str, key: str, update: Dict[str, str]):
    """更新指定配置项"""
    try:
        # 备份配置文件
        backup_file = backup_config()
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        # 如果配置节不存在，创建它
        if section not in config.sections():
            config.add_section(section)
        
        # 更新配置项
        value = update.get("value", "")
        config[section][key] = value
        
        # 保存配置文件
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return {
            "success": True,
            "data": {
                "section": section,
                "key": key,
                "value": value,
                "backup_file": backup_file
            },
            "message": f"配置项 {section}.{key} 更新成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置更新失败: {str(e)}")

@app.post("/api/config/section", summary="创建配置节")
async def create_config_section(section_data: ConfigSection):
    """创建新的配置节"""
    try:
        # 备份配置文件
        backup_file = backup_config()
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        # 检查配置节是否已存在
        if section_data.name in config.sections():
            raise HTTPException(status_code=409, detail=f"配置节 {section_data.name} 已存在")
        
        # 创建新配置节
        config.add_section(section_data.name)
        for key, value in section_data.items.items():
            config[section_data.name][key] = value
        
        # 保存配置文件
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return {
            "success": True,
            "data": {
                "section": section_data.name,
                "items": section_data.items,
                "backup_file": backup_file
            },
            "message": f"配置节 {section_data.name} 创建成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置节创建失败: {str(e)}")

@app.delete("/api/config/{section}", summary="删除配置节")
async def delete_config_section(section: str):
    """删除指定配置节"""
    try:
        # 备份配置文件
        backup_file = backup_config()
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        if section not in config.sections():
            raise HTTPException(status_code=404, detail=f"配置节 {section} 不存在")
        
        # 删除配置节
        config.remove_section(section)
        
        # 保存配置文件
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return {
            "success": True,
            "data": {
                "section": section,
                "backup_file": backup_file
            },
            "message": f"配置节 {section} 删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置节删除失败: {str(e)}")

@app.delete("/api/config/{section}/{key}", summary="删除配置项")
async def delete_config_item(section: str, key: str):
    """删除指定配置项"""
    try:
        # 备份配置文件
        backup_file = backup_config()
        
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE_PATH, encoding='utf-8')
        
        if section not in config.sections():
            raise HTTPException(status_code=404, detail=f"配置节 {section} 不存在")
        
        if key not in config[section]:
            raise HTTPException(status_code=404, detail=f"配置项 {section}.{key} 不存在")
        
        # 删除配置项
        config.remove_option(section, key)
        
        # 保存配置文件
        with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as f:
            config.write(f)
        
        return {
            "success": True,
            "data": {
                "section": section,
                "key": key,
                "backup_file": backup_file
            },
            "message": f"配置项 {section}.{key} 删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置项删除失败: {str(e)}")

# 网络接口管理API
@app.get("/api/network/interfaces", summary="获取网络接口列表")
async def get_network_interfaces():
    """获取系统网络接口列表"""
    try:
        # 使用ip命令获取网络接口信息
        result = run_system_command(["ip", "addr", "show"])
        
        if not result["success"]:
            raise HTTPException(status_code=500, detail=f"获取网络接口失败: {result['stderr']}")
        
        interfaces = []
        current_interface = None
        
        for line in result["stdout"].split("\n"):
            line = line.strip()
            if not line:
                continue
                
            # 解析接口信息
            if line[0].isdigit():
                # 新接口行
                parts = line.split(": ")
                if len(parts) >= 2:
                    interface_info = parts[1].split(" ")
                    interface_name = interface_info[0]
                    
                    current_interface = {
                        "name": interface_name,
                        "ip": None,
                        "netmask": None,
                        "gateway": None,
                        "dns": [],
                        "dhcp": False,
                        "status": "DOWN"
                    }
                    
                    # 检查接口状态
                    if "UP" in line:
                        current_interface["status"] = "UP"
                    
                    interfaces.append(current_interface)
            
            elif current_interface and "inet " in line:
                # IP地址行
                parts = line.split()
                for i, part in enumerate(parts):
                    if part == "inet" and i + 1 < len(parts):
                        ip_cidr = parts[i + 1]
                        if "/" in ip_cidr:
                            ip, cidr = ip_cidr.split("/")
                            current_interface["ip"] = ip
                            # 简单的CIDR到子网掩码转换
                            cidr_int = int(cidr)
                            if cidr_int == 24:
                                current_interface["netmask"] = "*************"
                            elif cidr_int == 16:
                                current_interface["netmask"] = "***********"
                            elif cidr_int == 8:
                                current_interface["netmask"] = "*********"
                        break
        
        return {
            "success": True,
            "data": interfaces,
            "message": "网络接口获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取网络接口失败: {str(e)}")

@app.get("/api/network/interfaces/{interface_name}", summary="获取指定网络接口信息")
async def get_network_interface(interface_name: str):
    """获取指定网络接口的详细信息"""
    try:
        result = run_system_command(["ip", "addr", "show", interface_name])
        
        if not result["success"]:
            raise HTTPException(status_code=404, detail=f"网络接口 {interface_name} 不存在")
        
        # 解析接口信息（简化版本）
        interface_info = {
            "name": interface_name,
            "ip": None,
            "netmask": None,
            "gateway": None,
            "dns": [],
            "dhcp": False,
            "status": "DOWN"
        }
        
        output = result["stdout"]
        if "UP" in output:
            interface_info["status"] = "UP"
        
        # 提取IP地址
        for line in output.split("\n"):
            if "inet " in line:
                parts = line.strip().split()
                for i, part in enumerate(parts):
                    if part == "inet" and i + 1 < len(parts):
                        ip_cidr = parts[i + 1]
                        if "/" in ip_cidr:
                            ip, cidr = ip_cidr.split("/")
                            interface_info["ip"] = ip
                        break
        
        return {
            "success": True,
            "data": interface_info,
            "message": f"网络接口 {interface_name} 信息获取成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取网络接口信息失败: {str(e)}")

@app.put("/api/network/interfaces/{interface_name}", summary="配置网络接口")
async def configure_network_interface(interface_name: str, config: NetworkConfig):
    """配置指定网络接口"""
    try:
        # 检查接口是否存在
        check_result = run_system_command(["ip", "link", "show", interface_name])
        if not check_result["success"]:
            raise HTTPException(status_code=404, detail=f"网络接口 {interface_name} 不存在")
        
        commands = []
        
        if config.dhcp:
            # DHCP配置
            commands.append(["dhclient", interface_name])
        else:
            # 静态IP配置
            if config.ip and config.netmask:
                # 删除现有IP地址
                commands.append(["ip", "addr", "flush", "dev", interface_name])
                
                # 设置新IP地址
                cidr = "24"  # 默认/24，实际应该根据子网掩码计算
                if config.netmask == "***********":
                    cidr = "16"
                elif config.netmask == "*********":
                    cidr = "8"
                
                commands.append(["ip", "addr", "add", f"{config.ip}/{cidr}", "dev", interface_name])
                
                # 启用接口
                commands.append(["ip", "link", "set", interface_name, "up"])
                
                # 设置网关
                if config.gateway:
                    commands.append(["ip", "route", "add", "default", "via", config.gateway])
        
        # 执行配置命令
        results = []
        for cmd in commands:
            result = run_system_command(cmd)
            results.append({
                "command": " ".join(cmd),
                "success": result["success"],
                "output": result["stdout"],
                "error": result["stderr"]
            })
        
        return {
            "success": True,
            "data": {
                "interface": interface_name,
                "config": config.dict(),
                "commands": results
            },
            "message": f"网络接口 {interface_name} 配置完成"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"网络接口配置失败: {str(e)}")

# 配置文件管理
@app.get("/api/config/backups", summary="获取配置文件备份列表")
async def get_config_backups():
    """获取配置文件备份列表"""
    try:
        create_backup_dir()
        backups = []
        
        for backup_file in BACKUP_DIR.glob("config_backup_*.ini"):
            stat = backup_file.stat()
            backups.append({
                "filename": backup_file.name,
                "path": str(backup_file),
                "size": stat.st_size,
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
            })
        
        # 按创建时间排序
        backups.sort(key=lambda x: x["created_time"], reverse=True)
        
        return {
            "success": True,
            "data": backups,
            "message": "配置文件备份列表获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取备份列表失败: {str(e)}")

@app.post("/api/config/backup", summary="手动备份配置文件")
async def manual_backup_config():
    """手动创建配置文件备份"""
    try:
        backup_file = backup_config()
        if backup_file:
            return {
                "success": True,
                "data": {
                    "backup_file": backup_file,
                    "timestamp": datetime.now().isoformat()
                },
                "message": "配置文件备份成功"
            }
        else:
            raise HTTPException(status_code=404, detail="配置文件不存在，无法备份")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置文件备份失败: {str(e)}")

@app.post("/api/config/restore/{backup_filename}", summary="恢复配置文件")
async def restore_config(backup_filename: str):
    """从备份恢复配置文件"""
    try:
        backup_file = BACKUP_DIR / backup_filename
        
        if not backup_file.exists():
            raise HTTPException(status_code=404, detail=f"备份文件 {backup_filename} 不存在")
        
        # 备份当前配置
        current_backup = backup_config()
        
        # 恢复配置文件
        import shutil
        shutil.copy2(backup_file, CONFIG_FILE_PATH)
        
        return {
            "success": True,
            "data": {
                "restored_from": backup_filename,
                "current_backup": current_backup,
                "timestamp": datetime.now().isoformat()
            },
            "message": f"配置文件从 {backup_filename} 恢复成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置文件恢复失败: {str(e)}")

@app.get("/api/config/download", summary="下载当前配置文件")
async def download_config():
    """下载当前配置文件"""
    if not CONFIG_FILE_PATH.exists():
        raise HTTPException(status_code=404, detail="配置文件不存在")
    
    return FileResponse(
        path=CONFIG_FILE_PATH,
        filename="config.ini",
        media_type="application/octet-stream"
    )

@app.post("/api/config/upload", summary="上传配置文件")
async def upload_config(file: UploadFile = File(...)):
    """上传新的配置文件"""
    try:
        if not file.filename or not file.filename.endswith('.ini'):
            raise HTTPException(status_code=400, detail="只支持.ini格式的配置文件")
        
        # 备份当前配置
        backup_file = backup_config()
        
        # 保存上传的文件
        content = await file.read()
        
        # 验证配置文件格式
        config = configparser.ConfigParser()
        try:
            config.read_string(content.decode('utf-8'))
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"配置文件格式错误: {str(e)}")
        
        # 保存配置文件
        with open(CONFIG_FILE_PATH, 'wb') as f:
            f.write(content)
        
        return {
            "success": True,
            "data": {
                "filename": file.filename,
                "size": len(content),
                "backup_file": backup_file,
                "timestamp": datetime.now().isoformat()
            },
            "message": "配置文件上传成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置文件上传失败: {str(e)}")

if __name__ == "__main__":
    # 自动启动SNMP桥接服务
    try:
        logger.info("🚀 正在启动SNMP桥接服务...")
        start_result = start_bridge_service()
        if start_result['success']:
            logger.info(f"✅ SNMP桥接服务启动成功 (PID: {start_result.get('pid')})")
        else:
            logger.warning(f"⚠️ SNMP桥接服务启动失败: {start_result['message']}")
    except Exception as e:
        logger.error(f"❌ 启动SNMP桥接服务异常: {e}")
    
    # 启动FastAPI服务
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )