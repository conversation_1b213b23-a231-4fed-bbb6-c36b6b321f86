#!/usr/bin/env python3
"""
健康检查模块

提供系统各组件的健康状态监控，包括Modbus连接、SNMP服务、
缓存系统等的健康检查功能。

主要功能：
1. 组件健康检查 - 检查各个组件的运行状态
2. HTTP健康检查端点 - 提供REST API接口
3. 自动健康监控 - 定期执行健康检查
4. 指标收集 - 收集性能和状态指标
5. 告警机制 - 健康状态异常时发送告警

作者: SNMP-Modbus Bridge Team V2
版本: 2.0.0
"""

import asyncio
import time
import logging
import json
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading

from utils.exceptions import HealthCheckError

logger = logging.getLogger(__name__)

class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"  
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"

@dataclass
class ComponentHealth:
    """组件健康状态"""
    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = field(default_factory=dict)
    last_check: float = field(default_factory=time.time)
    check_duration: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'status': self.status.value,
            'message': self.message,
            'details': self.details,
            'last_check': self.last_check,
            'check_duration': self.check_duration
        }

@dataclass
class SystemHealth:
    """系统整体健康状态"""
    overall_status: HealthStatus
    components: List[ComponentHealth] = field(default_factory=list)
    timestamp: float = field(default_factory=time.time)
    version: str = "2.0.0"
    uptime: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'overall_status': self.overall_status.value,
            'components': [comp.to_dict() for comp in self.components],
            'timestamp': self.timestamp,
            'version': self.version,
            'uptime': self.uptime,
            'summary': {
                'total_components': len(self.components),
                'healthy_components': len([c for c in self.components if c.status == HealthStatus.HEALTHY]),
                'degraded_components': len([c for c in self.components if c.status == HealthStatus.DEGRADED]),
                'unhealthy_components': len([c for c in self.components if c.status == HealthStatus.UNHEALTHY])
            }
        }

class HealthChecker:
    """健康检查器"""
    
    def __init__(self, check_interval: float = 30.0):
        """
        初始化健康检查器
        
        Args:
            check_interval: 检查间隔（秒）
        """
        self.check_interval = check_interval
        self.start_time = time.time()
        self.check_functions: Dict[str, Callable] = {}
        self.last_health_check: Optional[SystemHealth] = None
        self.is_running = False
        self._check_task = None
        
        logger.info(f"初始化健康检查器，检查间隔: {check_interval}秒")
    
    def register_check(self, component_name: str, check_function: Callable):
        """
        注册组件健康检查函数
        
        Args:
            component_name: 组件名称
            check_function: 检查函数，应返回ComponentHealth对象
        """
        self.check_functions[component_name] = check_function
        logger.debug(f"注册健康检查函数: {component_name}")
    
    async def check_component(self, component_name: str) -> ComponentHealth:
        """检查单个组件健康状态"""
        if component_name not in self.check_functions:
            return ComponentHealth(
                name=component_name,
                status=HealthStatus.UNKNOWN,
                message="未找到检查函数"
            )
        
        check_function = self.check_functions[component_name]
        start_time = time.time()
        
        try:
            logger.debug(f"开始检查组件: {component_name}")
            
            # 执行健康检查
            if asyncio.iscoroutinefunction(check_function):
                health = await check_function()
            else:
                health = check_function()
            
            # 确保返回的是ComponentHealth对象
            if not isinstance(health, ComponentHealth):
                health = ComponentHealth(
                    name=component_name,
                    status=HealthStatus.UNKNOWN,
                    message="检查函数返回类型错误"
                )
            
            health.check_duration = time.time() - start_time
            health.last_check = time.time()
            
            logger.debug(f"组件 {component_name} 检查完成: {health.status.value}")
            return health
            
        except Exception as e:
            logger.error(f"组件 {component_name} 健康检查异常: {e}")
            return ComponentHealth(
                name=component_name,
                status=HealthStatus.UNHEALTHY,
                message=f"检查异常: {str(e)}",
                check_duration=time.time() - start_time,
                last_check=time.time()
            )
    
    async def check_all_components(self) -> SystemHealth:
        """检查所有组件健康状态"""
        logger.debug("开始系统健康检查")
        start_time = time.time()
        
        # 并发检查所有组件
        tasks = []
        for component_name in self.check_functions.keys():
            task = asyncio.create_task(self.check_component(component_name))
            tasks.append(task)
        
        components = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        component_healths = []
        for i, result in enumerate(components):
            if isinstance(result, Exception):
                component_name = list(self.check_functions.keys())[i]
                component_healths.append(ComponentHealth(
                    name=component_name,
                    status=HealthStatus.UNHEALTHY,
                    message=f"检查任务异常: {str(result)}"
                ))
            else:
                component_healths.append(result)
        
        # 计算整体健康状态
        overall_status = self._calculate_overall_status(component_healths)
        
        system_health = SystemHealth(
            overall_status=overall_status,
            components=component_healths,
            timestamp=time.time(),
            uptime=time.time() - self.start_time
        )
        
        self.last_health_check = system_health
        
        check_duration = time.time() - start_time
        logger.info(f"系统健康检查完成: {overall_status.value}, 耗时: {check_duration:.2f}秒")
        
        return system_health
    
    def _calculate_overall_status(self, components: List[ComponentHealth]) -> HealthStatus:
        """计算整体健康状态"""
        if not components:
            return HealthStatus.UNKNOWN
        
        statuses = [comp.status for comp in components]
        
        # 如果有任何组件不健康，整体状态为不健康
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY
        
        # 如果有降级组件，整体状态为降级
        if HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED
        
        # 如果所有组件都健康，整体状态为健康
        if all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        
        # 其他情况返回未知
        return HealthStatus.UNKNOWN
    
    async def start_monitoring(self):
        """启动健康监控"""
        if self.is_running:
            return
        
        logger.info("启动健康监控")
        self.is_running = True
        self._check_task = asyncio.create_task(self._monitoring_loop())
    
    async def stop_monitoring(self):
        """停止健康监控"""
        if not self.is_running:
            return
        
        logger.info("停止健康监控")
        self.is_running = False
        
        if self._check_task:
            self._check_task.cancel()
            try:
                await self._check_task
            except asyncio.CancelledError:
                pass
            self._check_task = None
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await self.check_all_components()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康监控循环异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    def get_last_health_status(self) -> Optional[SystemHealth]:
        """获取最后的健康检查结果"""
        return self.last_health_check

class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP健康检查处理器"""
    
    def __init__(self, health_checker: HealthChecker, *args, **kwargs):
        self.health_checker = health_checker
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            if self.path == '/health':
                asyncio.run(self._handle_health_check())
            elif self.path == '/health/live':
                self._handle_liveness_check()
            elif self.path == '/health/ready':
                asyncio.run(self._handle_readiness_check())
            else:
                self._send_response(404, {"error": "Not Found"})
        except Exception as e:
            logger.error(f"HTTP健康检查异常: {e}")
            self._send_response(500, {"error": "Internal Server Error"})
    
    async def _handle_health_check(self):
        """处理完整健康检查"""
        health = await self.health_checker.check_all_components()
        
        status_code = 200
        if health.overall_status in [HealthStatus.DEGRADED, HealthStatus.UNHEALTHY]:
            status_code = 503
        
        self._send_response(status_code, health.to_dict())
    
    def _handle_liveness_check(self):
        """处理存活检查"""
        # 简单的存活检查，只要服务在运行就返回200
        self._send_response(200, {
            "status": "alive",
            "timestamp": time.time()
        })
    
    async def _handle_readiness_check(self):
        """处理就绪检查"""
        # 检查关键组件是否就绪
        last_health = self.health_checker.get_last_health_status()
        
        if last_health and last_health.overall_status == HealthStatus.HEALTHY:
            self._send_response(200, {
                "status": "ready",
                "timestamp": time.time()
            })
        else:
            self._send_response(503, {
                "status": "not_ready",
                "timestamp": time.time()
            })
    
    def _send_response(self, status_code: int, data: dict):
        """发送HTTP响应"""
        self.send_response(status_code)
        self.send_header('Content-type', 'application/json')
        self.send_header('Cache-Control', 'no-cache')
        self.end_headers()
        
        response_data = json.dumps(data, indent=2, ensure_ascii=False)
        self.wfile.write(response_data.encode('utf-8'))
    
    def log_message(self, format, *args):
        """重写日志方法，使用我们的日志器"""
        logger.debug(f"HTTP: {format % args}")

class HealthCheckServer:
    """健康检查HTTP服务器"""
    
    def __init__(self, health_checker: HealthChecker, host: str = '0.0.0.0', port: int = 8080):
        self.health_checker = health_checker
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
    
    def start(self):
        """启动HTTP服务器"""
        if self.server:
            return
        
        def handler(*args, **kwargs):
            return HealthCheckHandler(self.health_checker, *args, **kwargs)
        
        self.server = HTTPServer((self.host, self.port), handler)
        self.server_thread = threading.Thread(target=self.server.serve_forever)
        self.server_thread.daemon = True
        self.server_thread.start()
        
        logger.info(f"健康检查HTTP服务器启动: http://{self.host}:{self.port}")
    
    def stop(self):
        """停止HTTP服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            if self.server_thread:
                self.server_thread.join()
            self.server = None
            self.server_thread = None
            logger.info("健康检查HTTP服务器已停止")

# 为兼容性添加简化的健康检查器
class SimpleHealthChecker:
    """简化的健康检查器（兼容现有接口）"""
    
    def __init__(self, modbus_pool, cache=None, service_instance=None):
        self.modbus_pool = modbus_pool
        self.cache = cache
        self.service_instance = service_instance
        self.start_time = time.time()
        
        cache_status = "禁用" if cache is None else "启用"
        logger.info(f"初始化简化健康检查器（缓存: {cache_status}）")
    
    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            # 检查Modbus连接
            modbus_healthy = self.modbus_pool.is_connected
            
            # 检查缓存
            if self.cache:
                cache_stats = self.cache.get_stats()
                cache_status = 'healthy'
            else:
                cache_stats = {'disabled': True}
                cache_status = 'disabled'
            
            # 整体状态
            overall_status = "healthy" if modbus_healthy else "unhealthy"
            
            return {
                'status': overall_status,
                'timestamp': time.time(),
                'uptime': time.time() - self.start_time,
                'version': '2.2.0',
                'components': {
                    'modbus': {
                        'status': 'healthy' if modbus_healthy else 'unhealthy',
                        'connected': modbus_healthy,
                        'stats': self.modbus_pool.get_stats()
                    },
                    'cache': {
                        'status': cache_status,
                        'stats': cache_stats
                    },
                    'config_monitor': {
                        'status': 'healthy',
                        'stats': self.service_instance.config_monitor.get_stats() if self.service_instance else None
                    },
                    'hot_reload': {
                        'status': 'enabled',
                        'stats': self.service_instance.get_reload_stats() if self.service_instance else None
                    }
                }
            }
        
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': time.time()
            }