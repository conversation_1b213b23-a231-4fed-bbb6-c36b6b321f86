# API接口文档

本文档详细描述了SNMP-Modbus Bridge Web配置管理工具的REST API接口。

## 📖 概述

API基于RESTful设计原则，使用JSON格式进行数据交换。所有API接口都有统一的响应格式和错误处理机制。

### 基础信息
- **基础URL**: `http://localhost:8000/api`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 通用响应格式
```json
{
    "success": true,
    "data": {},
    "message": "操作成功"
}
```

### 错误响应格式
```json
{
    "detail": "错误描述信息"
}
```

## 🔧 配置管理API

### 获取完整配置

获取config.ini文件的所有配置内容。

**请求**
```http
GET /api/config
```

**响应示例**
```json
{
    "success": true,
    "data": {
        "SNMP_BRIDGE_CONFIG": {
            "listen_ip": "0.0.0.0",
            "listen_port": "1161",
            "community": "public",
            "modbus_type": "TCP"
        },
        "MODBUS_TCP_CONFIG": {
            "server_ip": "127.0.0.1",
            "port": "502",
            "timeout": "3"
        }
    },
    "message": "配置获取成功"
}
```

### 获取指定配置节

获取指定配置节的内容。

**请求**
```http
GET /api/config/{section}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| section | string | 是 | 配置节名称 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "section": "SNMP_BRIDGE_CONFIG",
        "items": {
            "listen_ip": "0.0.0.0",
            "listen_port": "1161",
            "community": "public",
            "modbus_type": "TCP"
        }
    },
    "message": "配置节 SNMP_BRIDGE_CONFIG 获取成功"
}
```

### 更新配置项

更新指定配置节中的配置项。

**请求**
```http
PUT /api/config/{section}/{key}
Content-Type: application/json

{
    "value": "新的配置值"
}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| section | string | 是 | 配置节名称 |
| key | string | 是 | 配置项键名 |

**请求体**
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| value | string | 是 | 新的配置值 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "section": "SNMP_BRIDGE_CONFIG",
        "key": "listen_port",
        "value": "1162",
        "backup_file": "/path/to/backup_20240905_142030.ini"
    },
    "message": "配置项 SNMP_BRIDGE_CONFIG.listen_port 更新成功"
}
```

### 创建配置节

创建新的配置节。

**请求**
```http
POST /api/config/section
Content-Type: application/json

{
    "name": "NEW_SECTION",
    "items": {
        "key1": "value1",
        "key2": "value2"
    }
}
```

**请求体**
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 配置节名称 |
| items | object | 是 | 配置项键值对 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "section": "NEW_SECTION",
        "items": {
            "key1": "value1",
            "key2": "value2"
        },
        "backup_file": "/path/to/backup_20240905_142030.ini"
    },
    "message": "配置节 NEW_SECTION 创建成功"
}
```

### 删除配置节

删除指定的配置节。

**请求**
```http
DELETE /api/config/{section}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| section | string | 是 | 配置节名称 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "section": "OLD_SECTION",
        "backup_file": "/path/to/backup_20240905_142030.ini"
    },
    "message": "配置节 OLD_SECTION 删除成功"
}
```

### 删除配置项

删除指定配置节中的配置项。

**请求**
```http
DELETE /api/config/{section}/{key}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| section | string | 是 | 配置节名称 |
| key | string | 是 | 配置项键名 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "section": "SNMP_BRIDGE_CONFIG",
        "key": "old_key",
        "backup_file": "/path/to/backup_20240905_142030.ini"
    },
    "message": "配置项 SNMP_BRIDGE_CONFIG.old_key 删除成功"
}
```

## 🌐 网络接口管理API

### 获取网络接口列表

获取系统所有网络接口的信息。

**请求**
```http
GET /api/network/interfaces
```

**响应示例**
```json
{
    "success": true,
    "data": [
        {
            "name": "eth0",
            "ip": "*************",
            "netmask": "*************",
            "gateway": "***********",
            "dns": ["*******", "*******"],
            "dhcp": false,
            "status": "UP"
        },
        {
            "name": "lo",
            "ip": "127.0.0.1",
            "netmask": "*********",
            "gateway": null,
            "dns": [],
            "dhcp": false,
            "status": "UP"
        }
    ],
    "message": "网络接口获取成功"
}
```

### 获取指定网络接口

获取指定网络接口的详细信息。

**请求**
```http
GET /api/network/interfaces/{interface_name}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| interface_name | string | 是 | 网络接口名称 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "name": "eth0",
        "ip": "*************",
        "netmask": "*************",
        "gateway": "***********",
        "dns": ["*******"],
        "dhcp": false,
        "status": "UP"
    },
    "message": "网络接口 eth0 信息获取成功"
}
```

### 配置网络接口

配置指定网络接口的网络参数。

**请求**
```http
PUT /api/network/interfaces/{interface_name}
Content-Type: application/json

{
    "interface": "eth0",
    "ip": "*************",
    "netmask": "*************",
    "gateway": "***********",
    "dns": ["*******", "*******"],
    "dhcp": false
}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| interface_name | string | 是 | 网络接口名称 |

**请求体**
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| interface | string | 是 | 网络接口名称 |
| ip | string | 否 | IP地址（DHCP模式时可选） |
| netmask | string | 否 | 子网掩码 |
| gateway | string | 否 | 网关地址 |
| dns | array | 否 | DNS服务器列表 |
| dhcp | boolean | 是 | 是否使用DHCP |

**响应示例**
```json
{
    "success": true,
    "data": {
        "interface": "eth0",
        "config": {
            "interface": "eth0",
            "ip": "*************",
            "netmask": "*************",
            "gateway": "***********",
            "dns": ["*******", "*******"],
            "dhcp": false
        },
        "commands": [
            {
                "command": "ip addr flush dev eth0",
                "success": true,
                "output": "",
                "error": ""
            },
            {
                "command": "ip addr add *************/24 dev eth0",
                "success": true,
                "output": "",
                "error": ""
            }
        ]
    },
    "message": "网络接口 eth0 配置完成"
}
```

## 💾 备份管理API

### 获取备份列表

获取所有配置文件备份的列表。

**请求**
```http
GET /api/config/backups
```

**响应示例**
```json
{
    "success": true,
    "data": [
        {
            "filename": "config_backup_20240905_142030.ini",
            "path": "/path/to/backup/config_backup_20240905_142030.ini",
            "size": 2048,
            "created_time": "2024-09-05T14:20:30.123456",
            "modified_time": "2024-09-05T14:20:30.123456"
        }
    ],
    "message": "配置文件备份列表获取成功"
}
```

### 创建手动备份

手动创建配置文件备份。

**请求**
```http
POST /api/config/backup
```

**响应示例**
```json
{
    "success": true,
    "data": {
        "backup_file": "/path/to/backup/config_backup_20240905_142030.ini",
        "timestamp": "2024-09-05T14:20:30.123456"
    },
    "message": "配置文件备份成功"
}
```

### 恢复配置文件

从备份文件恢复配置。

**请求**
```http
POST /api/config/restore/{backup_filename}
```

**路径参数**
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| backup_filename | string | 是 | 备份文件名 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "restored_from": "config_backup_20240905_142030.ini",
        "current_backup": "/path/to/backup/config_backup_20240905_143045.ini",
        "timestamp": "2024-09-05T14:30:45.123456"
    },
    "message": "配置文件从 config_backup_20240905_142030.ini 恢复成功"
}
```

### 下载当前配置文件

下载当前的配置文件。

**请求**
```http
GET /api/config/download
```

**响应**
- **Content-Type**: `application/octet-stream`
- **Content-Disposition**: `attachment; filename="config.ini"`

### 上传配置文件

上传新的配置文件替换当前配置。

**请求**
```http
POST /api/config/upload
Content-Type: multipart/form-data

file: [配置文件]
```

**请求体**
| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| file | file | 是 | .ini格式的配置文件 |

**响应示例**
```json
{
    "success": true,
    "data": {
        "filename": "config.ini",
        "size": 2048,
        "backup_file": "/path/to/backup/config_backup_20240905_142030.ini",
        "timestamp": "2024-09-05T14:20:30.123456"
    },
    "message": "配置文件上传成功"
}
```

## 🔍 系统状态API

### 健康检查

检查API服务的健康状态。

**请求**
```http
GET /api/health
```

**响应示例**
```json
{
    "status": "healthy",
    "timestamp": "2024-09-05T14:20:30.123456",
    "config_file_exists": true
}
```

### API根路径

获取API服务的基本信息。

**请求**
```http
GET /api/
```

**响应示例**
```json
{
    "service": "SNMP-Modbus Bridge Web配置管理工具",
    "version": "1.0.0",
    "status": "running",
    "timestamp": "2024-09-05T14:20:30.123456"
}
```

## 📊 HTTP状态码

| 状态码 | 描述 | 说明 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未授权访问 |
| 403 | Forbidden | 禁止访问 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 500 | Internal Server Error | 服务器内部错误 |

## 🔒 错误处理

### 错误响应格式
```json
{
    "detail": "具体的错误描述信息"
}
```

### 常见错误示例

#### 配置节不存在
```json
{
    "detail": "配置节 NON_EXISTENT_SECTION 不存在"
}
```

#### 配置文件格式错误
```json
{
    "detail": "配置文件格式错误: 无效的INI语法"
}
```

#### 网络接口不存在
```json
{
    "detail": "网络接口 invalid_interface 不存在"
}
```

#### 文件权限错误
```json
{
    "detail": "文件访问权限不足"
}
```

## 🧪 测试示例

### 使用curl测试API

#### 1. 健康检查
```bash
curl -X GET "http://localhost:8000/api/health" \
     -H "Accept: application/json"
```

#### 2. 获取配置
```bash
curl -X GET "http://localhost:8000/api/config" \
     -H "Accept: application/json"
```

#### 3. 更新配置项
```bash
curl -X PUT "http://localhost:8000/api/config/SNMP_BRIDGE_CONFIG/listen_port" \
     -H "Content-Type: application/json" \
     -d '{"value": "1162"}'
```

#### 4. 获取网络接口
```bash
curl -X GET "http://localhost:8000/api/network/interfaces" \
     -H "Accept: application/json"
```

#### 5. 配置网络接口
```bash
curl -X PUT "http://localhost:8000/api/network/interfaces/eth0" \
     -H "Content-Type: application/json" \
     -d '{
       "interface": "eth0",
       "ip": "*************",
       "netmask": "*************",
       "gateway": "***********",
       "dns": ["*******"],
       "dhcp": false
     }'
```

### 使用JavaScript测试

```javascript
// 获取配置
async function getConfig() {
    const response = await fetch('/api/config');
    const data = await response.json();
    console.log(data);
}

// 更新配置项
async function updateConfig(section, key, value) {
    const response = await fetch(`/api/config/${section}/${key}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ value: value })
    });
    const data = await response.json();
    console.log(data);
}

// 获取网络接口
async function getNetworkInterfaces() {
    const response = await fetch('/api/network/interfaces');
    const data = await response.json();
    console.log(data);
}
```

## 📝 开发指南

### API版本控制
当前API版本为v1，所有接口都在`/api`路径下。未来版本将使用`/api/v2`等路径。

### 请求限制
- 请求体大小限制：10MB
- 请求超时时间：30秒
- 并发请求限制：根据服务器配置

### 最佳实践
1. 始终检查响应的`success`字段
2. 处理所有可能的HTTP状态码
3. 实现适当的错误处理和重试机制
4. 在修改配置前创建备份

---

更多信息请参考[README.md](README.md)和在线API文档：http://localhost:8000/docs