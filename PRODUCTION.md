# 🏭 生产环境部署指南

## 概述

SNMP-Modbus Bridge Web配置管理工具支持两种部署模式：

1. **开发模式**: 前后端分离，前端运行在3000端口，后端运行在8000端口
2. **生产模式**: 前端构建为静态文件，由后端统一服务，只需8000端口

## 生产环境部署

### 📋 系统要求

- **操作系统**: Windows 10+ / Linux / macOS
- **Python**: 3.7+
- **Node.js**: 16+
- **内存**: 至少2GB
- **存储**: 至少1GB空闲空间

### 🏗️ 构建步骤

#### Step 1: 构建前端静态文件

**Windows:**
```cmd
cd web-config-manager
build.bat
```

**Linux/Mac:**
```bash
cd web-config-manager
chmod +x build.sh
./build.sh
```

#### Step 2: 验证构建结果

构建完成后，检查以下文件：
- `frontend/dist/index.html` - 主页面
- `frontend/dist/static/` - 静态资源目录

#### Step 3: 启动生产服务

**Windows:**
```cmd
start-prod.bat
```

**Linux/Mac:**
```bash
chmod +x start-prod.sh
./start-prod.sh
```

### 🌐 访问方式

生产环境下，所有服务通过单一端口访问：

- **Web管理界面**: http://localhost:8000
- **API接口**: http://localhost:8000/api
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

### 🔐 默认认证信息

- **用户名**: admin
- **密码**: admin1234

> ⚠️ **安全提示**: 生产环境部署前请修改默认密码！

## 架构说明

### 开发模式 vs 生产模式

**开发模式:**
```
浏览器 → 前端开发服务器(3000) → 后端API(8000)
```

**生产模式:**
```
浏览器 → 后端服务器(8000) → 静态文件 + API
```

### 生产模式优势

1. **单端口部署**: 简化网络配置和防火墙设置
2. **性能优化**: 前端资源经过压缩和优化
3. **缓存策略**: 静态资源支持浏览器缓存
4. **部署简单**: 无需Node.js运行时环境

## 部署配置

### 🔧 后端配置优化

编辑 `backend/main.py` 进行生产环境优化：

```python
# 生产环境CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # 替换为实际域名
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)
```

### 🌍 反向代理配置

#### Nginx配置示例

```nginx
server {
    listen 80;
    server_name yourdomain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location /static/ {
        proxy_pass http://localhost:8000/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Apache配置示例

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    
    ProxyPreserveHost On
    ProxyRequests Off
    ProxyPass / http://localhost:8000/
    ProxyPassReverse / http://localhost:8000/
    
    # 静态文件缓存
    <LocationMatch "^/static/">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

## 服务管理

### 🔄 系统服务配置

#### Linux systemd服务

创建 `/etc/systemd/system/snmp-bridge-web.service`:

```ini
[Unit]
Description=SNMP-Modbus Bridge Web Configuration Manager
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/web-config-manager/backend
ExecStart=/usr/bin/python3 main.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable snmp-bridge-web
sudo systemctl start snmp-bridge-web
```

#### Windows服务

使用 `nssm` 工具创建Windows服务：

```cmd
nssm install "SNMP Bridge Web" "C:\Python\python.exe"
nssm set "SNMP Bridge Web" AppParameters "main.py"
nssm set "SNMP Bridge Web" AppDirectory "C:\path\to\backend"
nssm start "SNMP Bridge Web"
```

### 📊 监控和日志

#### 日志配置

后端服务日志位置：
- **Linux**: `/var/log/snmp-modbus-bridge/`
- **Windows**: `logs/` 目录

#### 健康检查

```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查SNMP桥接服务
curl http://localhost:8000/api/bridge/status
```

## 安全加固

### 🔒 安全建议

1. **修改默认密码**
   ```python
   # 在config.ini中配置
   [WEB_AUTH]
   default_password = your_secure_password
   ```

2. **启用HTTPS**
   - 使用反向代理终止SSL
   - 或配置FastAPI直接支持HTTPS

3. **网络限制**
   - 配置防火墙规则
   - 使用VPN或内网访问

4. **访问日志**
   - 启用详细的访问日志
   - 定期审核用户操作

### 🛡️ 防火墙配置

**Linux iptables:**
```bash
# 允许8000端口
iptables -A INPUT -p tcp --dport 8000 -j ACCEPT

# 限制特定IP访问
iptables -A INPUT -p tcp --dport 8000 -s ***********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 8000 -j DROP
```

**Windows防火墙:**
```cmd
netsh advfirewall firewall add rule name="SNMP Bridge Web" dir=in action=allow protocol=TCP localport=8000
```

## 故障排除

### 🔍 常见问题

#### 1. 前端构建失败
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 2. 静态文件无法访问
- 检查 `frontend/dist` 目录是否存在
- 验证文件权限设置
- 查看后端日志中的静态文件配置

#### 3. API请求失败
- 检查CORS配置
- 验证认证令牌
- 查看后端错误日志

#### 4. 服务无法启动
- 检查端口8000是否被占用
- 验证Python依赖安装
- 查看详细错误信息

### 📝 日志分析

```bash
# 查看实时日志
tail -f logs/backend.log

# 搜索错误信息
grep -i error logs/backend.log

# 分析访问模式
grep "GET\|POST\|PUT\|DELETE" logs/backend.log
```

## 性能优化

### ⚡ 优化建议

1. **静态资源缓存**
   - 配置适当的缓存头
   - 使用CDN分发静态资源

2. **数据库连接池**
   - 配置合适的连接池大小
   - 监控连接使用情况

3. **内存管理**
   - 定期重启服务释放内存
   - 监控内存使用趋势

4. **并发限制**
   - 配置适当的worker数量
   - 限制并发请求数

## 备份和恢复

### 💾 备份策略

1. **配置文件备份**
   - 定期备份 `config.ini`
   - 使用版本控制管理配置

2. **日志备份**
   - 定期轮转和压缩日志
   - 保留必要的历史记录

3. **应用备份**
   - 备份整个应用目录
   - 包含所有依赖和配置

### 🔄 灾难恢复

1. **快速恢复流程**
   - 准备标准化部署脚本
   - 维护配置文件模板
   - 建立监控告警机制

---

📞 **技术支持**: 如有问题，请参考故障排除指南或联系系统管理员。